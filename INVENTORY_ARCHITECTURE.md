# Arquitectura del Sistema de Inventario

## 📋 Visión General

El sistema de inventario es un módulo completo para la gestión de productos y costos en salones de belleza, integrado profundamente con el flujo de servicios de coloración.

## 🏗️ Arquitectura de Componentes

```
┌─────────────────────────────────────────────────────────────┐
│                        UI Layer                              │
├─────────────────────────────────────────────────────────────┤
│  Screens:                   │  Components:                  │
│  - inventory.tsx           │  - PricingSetupModal         │
│  - inventory/new.tsx       │  - StockMovementsModal       │
│  - inventory/[id].tsx      │  - InventoryReports          │
│  - inventory/edit/[id].tsx │  - PricingSettingsModal      │
│  - settings.tsx            │                               │
├─────────────────────────────────────────────────────────────┤
│                      Service Layer                           │
├─────────────────────────────────────────────────────────────┤
│  - inventoryConsumptionService.ts                          │
│    • Product matching algorithm                             │
│    • Stock validation                                       │
│    • Cost calculation                                       │
│    • Automatic consumption                                  │
├─────────────────────────────────────────────────────────────┤
│                       Store Layer                            │
├─────────────────────────────────────────────────────────────┤
│  - inventory-store.ts      │  - salon-config-store.ts      │
│    • Products CRUD         │    • Pricing configuration    │
│    • Stock movements       │    • Control levels           │
│    • Alerts system         │    • Currency formatting      │
│    • Reports generation    │    • Business settings        │
├─────────────────────────────────────────────────────────────┤
│                    Persistence Layer                         │
├─────────────────────────────────────────────────────────────┤
│              AsyncStorage (zustand persist)                  │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 Flujo de Datos

### 1. Creación de Servicio → Consumo de Inventario

```
Usuario crea servicio
    ↓
FormulationStep
    ↓
inventoryConsumptionService.calculateFormulationCost()
    ├→ Busca productos en inventario (matching inteligente)
    ├→ Calcula costos reales
    └→ Valida disponibilidad
    ↓
CompletionStep
    ↓
inventoryConsumptionService.consumeFormulation()
    ├→ Registra movimientos de stock
    ├→ Actualiza cantidades
    └→ Genera alertas si es necesario
```

### 2. Sistema de 3 Niveles de Control

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Solo Fórmulas  │     │   Smart Cost    │     │  Control Total  │
├─────────────────┤     ├─────────────────┤     ├─────────────────┤
│ • Sin inventario│     │ • Inventario    │     │ • Todo Smart    │
│ • Sin costos    │     │ • Costos reales │     │   Cost +        │
│ • Solo fórmulas │     │ • Sin consumo   │     │ • Consumo auto  │
│                 │     │   automático    │     │ • Movimientos   │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## 🧩 Componentes Principales

### Stores

#### `inventory-store.ts`
- **Responsabilidad**: Gestión completa del inventario
- **Características**:
  - CRUD de productos con validación
  - Sistema de movimientos con trazabilidad
  - Alertas automáticas de stock bajo
  - Generación de reportes y análisis
  - Mock data para testing

#### `salon-config-store.ts`
- **Responsabilidad**: Configuración del negocio
- **Características**:
  - Niveles de control de inventario
  - Configuración de precios y márgenes
  - Políticas de redondeo
  - Formato de moneda

### Servicios

#### `inventoryConsumptionService.ts`
- **Responsabilidad**: Lógica de consumo y cálculo
- **Algoritmo de Matching**:
  ```typescript
  1. Normaliza nombres de productos
  2. Busca coincidencias exactas
  3. Busca coincidencias parciales
  4. Prioriza por categoría
  5. Retorna mejor match o null
  ```

### Componentes UI

#### Modal de Movimientos
- Entrada/Salida/Ajuste de stock
- Validación en tiempo real
- Feedback visual inmediato

#### Reportes de Inventario
- Cards de resumen visual
- Productos más/menos usados
- Distribución por categoría
- Sin dependencias de gráficos externos

## 🔐 Seguridad y Validaciones

1. **Validación de Stock**
   - Previene consumo con stock insuficiente
   - Alertas proactivas de stock bajo

2. **Control de Acceso**
   - Funciones según nivel de control
   - UI adaptativa por permisos

3. **Integridad de Datos**
   - Transacciones atómicas
   - Trazabilidad completa
   - Sin eliminación de histórico

## 🚀 Extensibilidad

### Agregar Nueva Funcionalidad

1. **Nuevo tipo de movimiento**:
   ```typescript
   // En types/inventory.ts
   type: 'entrada' | 'salida' | 'ajuste' | 'consumo' | 'devolucion'
   ```

2. **Nueva categoría de producto**:
   ```typescript
   // En types/inventory.ts
   category: 'tinte' | 'oxidante' | 'decolorante' | 'tratamiento' | 'herramienta' | 'otro'
   ```

3. **Nuevo reporte**:
   - Agregar método en inventory-store
   - Crear componente en components/reports
   - Integrar en UI principal

### Integraciones Futuras

- **API Backend**: Reemplazar AsyncStorage con API REST
- **Códigos de Barras**: Integrar scanner para entrada rápida
- **Proveedores**: Sistema de órdenes de compra
- **Multi-sucursal**: Compartir inventario entre locaciones

## 📊 Métricas de Performance

- **Tiempo de carga inicial**: < 500ms
- **Búsqueda de productos**: < 100ms
- **Generación de reportes**: < 200ms
- **Persistencia de datos**: Inmediata

## 🧪 Testing

### Flujos Críticos a Validar

1. **Crear servicio → Consumir stock → Ver reporte**
2. **Cambiar nivel de control → Verificar persistencia**
3. **Stock bajo → Alerta → Reposición → Limpiar alerta**
4. **Configurar precios → Crear servicio → Verificar cálculos**

### Edge Cases Manejados

- Productos sin precio definido
- Stock negativo prevenido
- Consumo mayor al disponible
- Cambios de configuración en medio de servicio
- Productos duplicados

## 📝 Notas de Mantenimiento

1. **Mock Data**: Los movimientos históricos se generan solo en la primera inicialización
2. **Limpieza**: Las alertas antiguas se pueden limpiar manualmente
3. **Performance**: Los reportes se calculan on-demand, no pre-computados
4. **Migración**: El schema de datos permite agregar campos sin romper compatibilidad

## 🎯 Decisiones de Diseño

1. **Sin gráficos complejos**: Prioridad en funcionalidad sobre visualización
2. **Matching flexible**: Permite trabajar sin configuración perfecta
3. **3 niveles**: Balance entre simplicidad y control completo
4. **Mock data rica**: Facilita testing y demos realistas
5. **UI consistente**: Reutilización de patrones existentes

---

Este sistema está diseñado para escalar desde un salón individual hasta cadenas multi-sucursal, manteniendo la simplicidad para usuarios básicos mientras ofrece control total para usuarios avanzados.