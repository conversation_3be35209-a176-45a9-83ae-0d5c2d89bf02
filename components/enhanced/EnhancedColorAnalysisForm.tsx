/**
 * Enhanced Color Analysis Form
 * Versión simplificada que funciona inmediatamente
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert
} from 'react-native';
import { Zap, Eye, CheckCircle, AlertTriangle, ChevronRight, Brain } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import BaseButton from '@/components/base/BaseButton';

interface EnhancedColorAnalysisFormProps {
  analysisResult?: DesiredColorAnalysisResult;
  onAnalysisChange: (result: DesiredColorAnalysisResult) => void;
  isFromAI?: boolean;
  currentHairState?: any;
  mode?: 'current-color' | 'desired-color' | 'full-consultation';
  professionalLevel?: 'beginner' | 'intermediate' | 'expert';
  showAIFeatures?: boolean;
}

export default function EnhancedColorAnalysisForm({
  analysisResult,
  onAnalysisChange,
  isFromAI = false,
  currentHairState,
  mode = 'desired-color',
  professionalLevel = 'intermediate',
  showAIFeatures = true
}: EnhancedColorAnalysisFormProps) {
  const [currentLevel, setCurrentLevel] = useState(1);
  const [formData, setFormData] = useState({
    goal: '',
    targetLevel: '',
    targetTone: '',
    technique: '',
    maintenance: '',
    budget: ''
  });
  const [aiSuggestions, setAiSuggestions] = useState<Record<string, any>>({});

  // Simular sugerencias de IA
  const triggerAISuggestions = () => {
    const suggestions = {
      targetLevel: '8',
      targetTone: 'Rubio ceniza',
      technique: 'Balayage',
      confidence: 85
    };
    setAiSuggestions(suggestions);

    Alert.alert(
      '🤖 IA Activada',
      `Sugerencias generadas con ${suggestions.confidence}% de confianza:\n\n• Nivel: ${suggestions.targetLevel}\n• Tono: ${suggestions.targetTone}\n• Técnica: ${suggestions.technique}`,
      [
        { text: 'Rechazar', style: 'cancel' },
        { text: 'Aplicar', onPress: () => applyAISuggestions(suggestions) }
      ]
    );
  };

  const applyAISuggestions = (suggestions: any) => {
    const newData = {
      ...formData,
      targetLevel: suggestions.targetLevel,
      targetTone: suggestions.targetTone,
      technique: suggestions.technique
    };
    setFormData(newData);
    updateLegacyResult(newData);
  };

  const handleFieldChange = (fieldId: string, value: any) => {
    const newData = { ...formData, [fieldId]: value };
    setFormData(newData);
    updateLegacyResult(newData);
  };

  const updateLegacyResult = (data: any) => {
    const legacyResult: DesiredColorAnalysisResult = {
      general: {
        overallLevel: data.targetLevel || '',
        overallTone: data.targetTone || '',
        technique: data.technique || '',
        customTechnique: ''
      },
      zones: {},
      lifestyle: {
        maintenanceLevel: data.maintenance as any,
        budgetLevel: data.budget as any,
        avoidableTones: []
      },
      isFromAI: isFromAI
    };
    onAnalysisChange(legacyResult);
  };

  const nextLevel = () => {
    if (currentLevel < 3) {
      setCurrentLevel(currentLevel + 1);
    }
  };

  const prevLevel = () => {
    if (currentLevel > 1) {
      setCurrentLevel(currentLevel - 1);
    }
  };

  const renderLevel1 = () => (
    <View style={styles.levelContainer}>
      <Text style={styles.levelTitle}>🎯 Nivel 1: Inicio Rápido (30 segundos)</Text>

      <View style={styles.fieldGroup}>
        <Text style={styles.fieldLabel}>¿Qué quieres lograr hoy?</Text>
        <View style={styles.optionsContainer}>
          {['Mantener color actual', 'Cambio sutil', 'Transformación dramática', 'Corrección de color'].map((option) => (
            <TouchableOpacity
              key={option}
              style={[styles.optionButton, formData.goal === option && styles.optionButtonSelected]}
              onPress={() => handleFieldChange('goal', option)}
            >
              <Text style={[styles.optionText, formData.goal === option && styles.optionTextSelected]}>
                {option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {showAIFeatures && (
        <TouchableOpacity style={styles.aiButton} onPress={triggerAISuggestions}>
          <Brain size={20} color={Colors.light.primary} />
          <Text style={styles.aiButtonText}>Obtener Sugerencias IA</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderLevel2 = () => (
    <View style={styles.levelContainer}>
      <Text style={styles.levelTitle}>🎨 Nivel 2: Consulta Inteligente (2-3 min)</Text>

      <View style={styles.fieldGroup}>
        <Text style={styles.fieldLabel}>Nivel objetivo (1-10)</Text>
        <TextInput
          style={styles.input}
          value={formData.targetLevel}
          onChangeText={(value) => handleFieldChange('targetLevel', value)}
          placeholder="Ej: 8"
          keyboardType="numeric"
        />
        {aiSuggestions.targetLevel && (
          <Text style={styles.aiSuggestion}>💡 IA sugiere: {aiSuggestions.targetLevel}</Text>
        )}
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.fieldLabel}>Tono deseado</Text>
        <TextInput
          style={styles.input}
          value={formData.targetTone}
          onChangeText={(value) => handleFieldChange('targetTone', value)}
          placeholder="Ej: Rubio ceniza"
        />
        {aiSuggestions.targetTone && (
          <Text style={styles.aiSuggestion}>💡 IA sugiere: {aiSuggestions.targetTone}</Text>
        )}
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.fieldLabel}>Técnica preferida</Text>
        <View style={styles.optionsContainer}>
          {['Color uniforme', 'Balayage', 'Mechas', 'Ombré'].map((option) => (
            <TouchableOpacity
              key={option}
              style={[styles.optionButton, formData.technique === option && styles.optionButtonSelected]}
              onPress={() => handleFieldChange('technique', option)}
            >
              <Text style={[styles.optionText, formData.technique === option && styles.optionTextSelected]}>
                {option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        {aiSuggestions.technique && (
          <Text style={styles.aiSuggestion}>💡 IA sugiere: {aiSuggestions.technique}</Text>
        )}
      </View>
    </View>
  );

  const renderLevel3 = () => (
    <View style={styles.levelContainer}>
      <Text style={styles.levelTitle}>🔬 Nivel 3: Modo Experto (Opcional)</Text>

      <View style={styles.fieldGroup}>
        <Text style={styles.fieldLabel}>Nivel de mantenimiento</Text>
        <View style={styles.optionsContainer}>
          {['Bajo', 'Medio', 'Alto'].map((option) => (
            <TouchableOpacity
              key={option}
              style={[styles.optionButton, formData.maintenance === option && styles.optionButtonSelected]}
              onPress={() => handleFieldChange('maintenance', option)}
            >
              <Text style={[styles.optionText, formData.maintenance === option && styles.optionTextSelected]}>
                {option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.fieldLabel}>Rango de presupuesto</Text>
        <View style={styles.optionsContainer}>
          {['Económico', 'Medio', 'Premium'].map((option) => (
            <TouchableOpacity
              key={option}
              style={[styles.optionButton, formData.budget === option && styles.optionButtonSelected]}
              onPress={() => handleFieldChange('budget', option)}
            >
              <Text style={[styles.optionText, formData.budget === option && styles.optionTextSelected]}>
                {option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Brain size={24} color={Colors.light.primary} />
          <Text style={styles.title}>Formulario Avanzado con IA</Text>
        </View>
        <Text style={styles.subtitle}>Experiencia progresiva optimizada</Text>
      </View>

      {/* Progress Indicator */}
      <View style={styles.progressContainer}>
        {[1, 2, 3].map((level) => (
          <View key={level} style={styles.progressStep}>
            <View style={[
              styles.progressDot,
              level <= currentLevel && styles.progressDotActive,
              level < currentLevel && styles.progressDotCompleted
            ]}>
              <Text style={[
                styles.progressDotText,
                level <= currentLevel && styles.progressDotTextActive
              ]}>
                {level < currentLevel ? '✓' : level}
              </Text>
            </View>
            <Text style={styles.progressLabel}>
              {level === 1 ? 'Rápido' : level === 2 ? 'Inteligente' : 'Experto'}
            </Text>
          </View>
        ))}
      </View>

      {/* Content */}
      <ScrollView style={styles.content}>
        {currentLevel === 1 && renderLevel1()}
        {currentLevel === 2 && renderLevel2()}
        {currentLevel === 3 && renderLevel3()}
      </ScrollView>

      {/* Navigation */}
      <View style={styles.navigation}>
        <BaseButton
          title="Anterior"
          variant="outline"
          size="md"
          onPress={prevLevel}
          disabled={currentLevel === 1}
          style={styles.navButton}
        />

        <BaseButton
          title={currentLevel === 3 ? "Completar" : "Siguiente"}
          variant="primary"
          size="md"
          icon={ChevronRight}
          iconPosition="right"
          onPress={currentLevel === 3 ? () => Alert.alert('¡Completado!', 'Formulario avanzado completado con éxito') : nextLevel}
          style={styles.navButton}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    backgroundColor: Colors.light.surface,
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  aiIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primaryLight + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.full,
    gap: spacing.xs,
  },
  aiText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  modeToggle: {
    alignSelf: 'flex-end',
  },
  modeText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xs,
  },
  fallbackContainer: {
    flex: 1,
    padding: spacing.lg,
  },
  fallbackText: {
    fontSize: typography.sizes.md,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xl,
    fontStyle: 'italic',
  },
  analysisIndicator: {
    position: 'absolute',
    bottom: spacing.lg,
    left: spacing.lg,
    right: spacing.lg,
    backgroundColor: Colors.light.primary,
    padding: spacing.md,
    borderRadius: radius.md,
    alignItems: 'center',
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    paddingHorizontal: 20,
    backgroundColor: Colors.light.surface,
  },
  progressStep: {
    alignItems: 'center',
    flex: 1,
  },
  progressDot: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.borderLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressDotActive: {
    backgroundColor: Colors.light.primary,
  },
  progressDotCompleted: {
    backgroundColor: Colors.light.success,
  },
  progressDotText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.light.textSecondary,
  },
  progressDotTextActive: {
    color: Colors.light.textLight,
  },
  progressLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  levelContainer: {
    gap: 20,
  },
  levelTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 16,
  },
  fieldGroup: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  input: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  optionButtonSelected: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  optionText: {
    fontSize: 14,
    color: Colors.light.text,
    fontWeight: '500',
  },
  optionTextSelected: {
    color: Colors.light.textLight,
  },
  aiButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.primaryLight + '20',
    padding: 16,
    borderRadius: 12,
    gap: 8,
    marginTop: 16,
  },
  aiButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  aiSuggestion: {
    fontSize: 14,
    color: Colors.light.primary,
    marginTop: 8,
    fontStyle: 'italic',
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: Colors.light.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  navButton: {
    flex: 0.45,
  },
});
