/**
 * Enhanced Color Analysis Form
 * Reemplazo del DesiredColorAnalysisForm con Progressive Disclosure
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert
} from 'react-native';
import { Zap, Eye, CheckCircle, AlertTriangle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';
import ProgressiveForm from '@/components/progressive/ProgressiveForm';
import {
  DesiredColorAnalysisResult,
  AIConfidenceScore,
  ConsultationContext
} from '@/types/progressive-disclosure';
import { getFormConfig, customizeFormConfig } from '@/config/colorationFormConfigs';
import { useAIAnalysisStore } from '@/stores/ai-analysis-store';
import BaseButton from '@/components/base/BaseButton';

interface EnhancedColorAnalysisFormProps {
  analysisResult?: DesiredColorAnalysisResult;
  onAnalysisChange: (result: DesiredColorAnalysisResult) => void;
  isFromAI?: boolean;
  currentHairState?: any;
  clientHistory?: any;
  mode?: 'current-color' | 'desired-color' | 'full-consultation';
  professionalLevel?: 'beginner' | 'intermediate' | 'expert';
  timeConstraints?: number;
  showAIFeatures?: boolean;
}

export default function EnhancedColorAnalysisForm({
  analysisResult,
  onAnalysisChange,
  isFromAI = false,
  currentHairState,
  clientHistory,
  mode = 'desired-color',
  professionalLevel = 'intermediate',
  timeConstraints,
  showAIFeatures = true
}: EnhancedColorAnalysisFormProps) {
  const [useProgressiveMode, setUseProgressiveMode] = useState(true);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [aiConfidence, setAiConfidence] = useState<AIConfidenceScore | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const { analyzeDesiredPhoto, isAnalyzingDesiredPhoto } = useAIAnalysisStore();

  // Configurar formulario según el modo
  const baseConfig = getFormConfig(mode);
  const context: ConsultationContext = {
    clientHistory,
    currentHairState,
    professionalLevel,
    timeConstraints
  };
  const formConfig = customizeFormConfig(baseConfig, context);

  // Convertir datos existentes al nuevo formato
  useEffect(() => {
    if (analysisResult) {
      const convertedData = convertLegacyData(analysisResult);
      setFormData(convertedData);
    }
  }, [analysisResult]);

  // Manejar cambios en campos
  const handleFieldChange = useCallback((fieldId: string, value: any) => {
    const newData = { ...formData, [fieldId]: value };
    setFormData(newData);

    // Convertir de vuelta al formato legacy para compatibilidad
    const legacyResult = convertToLegacyFormat(newData);
    onAnalysisChange(legacyResult);

    // Trigger AI analysis si es necesario
    if (fieldId === 'reference_photo' && value && showAIFeatures) {
      triggerAIAnalysis(value);
    }
  }, [formData, onAnalysisChange, showAIFeatures]);

  // Manejar sugerencias de IA
  const handleAISuggestions = useCallback((
    suggestions: Record<string, any>,
    confidence: AIConfidenceScore
  ) => {
    setAiConfidence(confidence);
    
    // Aplicar sugerencias con alta confianza
    const updatedData = { ...formData };
    Object.entries(suggestions).forEach(([fieldId, value]) => {
      const fieldConfidence = confidence.fieldScores[fieldId] || 0;
      if (fieldConfidence > 80) {
        updatedData[fieldId] = value;
      }
    });
    
    setFormData(updatedData);
    const legacyResult = convertToLegacyFormat(updatedData);
    onAnalysisChange(legacyResult);
  }, [formData, onAnalysisChange]);

  // Completar formulario
  const handleFormComplete = useCallback((data: Record<string, any>) => {
    const legacyResult = convertToLegacyFormat(data);
    onAnalysisChange(legacyResult);
    
    Alert.alert(
      'Análisis Completado',
      'El análisis de color ha sido completado exitosamente.',
      [{ text: 'Continuar', style: 'default' }]
    );
  }, [onAnalysisChange]);

  // Trigger análisis de IA
  const triggerAIAnalysis = async (photoUri: string) => {
    if (!showAIFeatures) return;

    setIsAnalyzing(true);
    try {
      const analysis = await analyzeDesiredPhoto('temp-id', photoUri, 5);
      
      if (analysis) {
        const suggestions = convertAIAnalysisToSuggestions(analysis);
        const confidence: AIConfidenceScore = {
          overall: 85,
          fieldScores: {
            target_level: 90,
            target_tone: 85,
            technique_preference: 75
          },
          reasoning: [
            'Análisis basado en reconocimiento de patrones de color',
            'Comparación con base de datos de transformaciones exitosas',
            'Evaluación de viabilidad técnica'
          ],
          suggestions: [
            'El color detectado es altamente viable',
            'Se recomienda técnica de balayage para mejor resultado',
            'Considerar pre-aclarado en algunas zonas'
          ]
        };
        
        handleAISuggestions(suggestions, confidence);
      }
    } catch (error) {
      console.error('Error en análisis IA:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Alternar entre modo progresivo y clásico
  const toggleMode = () => {
    setUseProgressiveMode(!useProgressiveMode);
  };

  // Convertir datos legacy al nuevo formato
  const convertLegacyData = (legacy: DesiredColorAnalysisResult): Record<string, any> => {
    return {
      target_level: legacy.general?.overallLevel || '',
      target_tone: legacy.general?.overallTone || '',
      technique_preference: legacy.general?.technique || '',
      maintenance_level: legacy.lifestyle?.maintenanceLevel || '',
      budget_range: legacy.lifestyle?.budgetLevel || '',
      // Mapear otros campos según sea necesario
    };
  };

  // Convertir al formato legacy para compatibilidad
  const convertToLegacyFormat = (data: Record<string, any>): DesiredColorAnalysisResult => {
    return {
      general: {
        overallLevel: data.target_level || '',
        overallTone: data.target_tone || '',
        technique: data.technique_preference || '',
        customTechnique: data.advanced_technique || ''
      },
      zones: {}, // Mapear datos de zonas si existen
      lifestyle: {
        maintenanceLevel: data.maintenance_level as any,
        budgetLevel: data.budget_range as any,
        avoidableTones: []
      },
      isFromAI: isFromAI || !!aiConfidence
    };
  };

  // Convertir análisis de IA a sugerencias
  const convertAIAnalysisToSuggestions = (analysis: any): Record<string, any> => {
    return {
      target_level: analysis.level || '',
      target_tone: analysis.tone || '',
      technique_preference: analysis.recommendedTechnique || 'balayage'
    };
  };

  if (!useProgressiveMode) {
    // Fallback al formulario original para compatibilidad
    return (
      <View style={styles.fallbackContainer}>
        <View style={styles.modeToggle}>
          <Text style={styles.modeText}>Modo Clásico Activo</Text>
          <BaseButton
            title="Probar Modo Avanzado"
            variant="outline"
            size="sm"
            icon={Zap}
            onPress={toggleMode}
          />
        </View>
        {/* Aquí iría el formulario original como fallback */}
        <Text style={styles.fallbackText}>
          Formulario clásico (implementar fallback al componente original)
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header con opciones */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{formConfig.title}</Text>
            <Text style={styles.subtitle}>{formConfig.description}</Text>
          </View>
          
          {showAIFeatures && (
            <View style={styles.aiIndicator}>
              <Zap size={16} color={Colors.light.primary} />
              <Text style={styles.aiText}>IA Activa</Text>
            </View>
          )}
        </View>

        <View style={styles.modeToggle}>
          <BaseButton
            title="Modo Clásico"
            variant="ghost"
            size="sm"
            icon={Eye}
            onPress={toggleMode}
          />
        </View>
      </View>

      {/* Formulario progresivo */}
      <ProgressiveForm
        formId={`color-analysis-${mode}`}
        config={formConfig}
        context={context}
        onFieldChange={handleFieldChange}
        onFormComplete={handleFormComplete}
        onAISuggestion={handleAISuggestions}
        initialData={formData}
        showAIIndicators={showAIFeatures}
        allowLevelSkipping={professionalLevel === 'expert'}
      />

      {/* Indicadores de estado */}
      {isAnalyzing && (
        <View style={styles.analysisIndicator}>
          <Text style={styles.analysisText}>Analizando con IA...</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: Colors.light.surface,
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    ...shadows.sm,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.sizes.md,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  aiIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primaryLight + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.full,
    gap: spacing.xs,
  },
  aiText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  modeToggle: {
    alignSelf: 'flex-end',
  },
  modeText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xs,
  },
  fallbackContainer: {
    flex: 1,
    padding: spacing.lg,
  },
  fallbackText: {
    fontSize: typography.sizes.md,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xl,
    fontStyle: 'italic',
  },
  analysisIndicator: {
    position: 'absolute',
    bottom: spacing.lg,
    left: spacing.lg,
    right: spacing.lg,
    backgroundColor: Colors.light.primary,
    padding: spacing.md,
    borderRadius: radius.md,
    alignItems: 'center',
  },
  analysisText: {
    color: Colors.light.textLight,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
  },
});
