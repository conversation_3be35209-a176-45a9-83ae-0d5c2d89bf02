import React from "react";
import { StyleSheet, Text, View, TouchableOpacity, Modal, FlatList, Animated } from "react-native";
import { ChevronDown, Check } from "lucide-react-native";
import Colors from "@/constants/colors";

interface DiagnosisSelectorProps {
  label: string;
  value: string;
  options: string[] | number[];
  onValueChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  isFromAI?: boolean;
}

export default function DiagnosisSelector({
  label,
  value,
  options,
  onValueChange,
  placeholder = "Seleccionar",
  required = false,
  isFromAI = false
}: DiagnosisSelectorProps) {
  const [isModalVisible, setIsModalVisible] = React.useState(false);
  const [hasBeenEdited, setHasBeenEdited] = React.useState(false);
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  
  // Debug log
  React.useEffect(() => {
    if (value) {
      console.log(`DiagnosisSelector [${label}] - Value:`, value);
    }
  }, [value, label]);

  // Animate when value appears from AI
  React.useEffect(() => {
    if (value && isFromAI && !hasBeenEdited) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [value, isFromAI, hasBeenEdited, fadeAnim]);

  const handleSelect = (option: string | number) => {
    const value = typeof option === 'number' && option % 1 !== 0 
      ? option.toFixed(1) 
      : option.toString();
    onValueChange(value);
    setIsModalVisible(false);
    setHasBeenEdited(true);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {label} {required && <Text style={styles.required}>*</Text>}
        {value && isFromAI && (
          <Animated.Text style={[styles.indicator, { opacity: fadeAnim }]}>
            {hasBeenEdited ? " ✓" : " •"}
          </Animated.Text>
        )}
      </Text>
      
      <TouchableOpacity
        style={styles.selector}
        onPress={() => setIsModalVisible(true)}
      >
        <Text style={[styles.selectorText, !value && styles.placeholderText]}>
          {value || placeholder}
        </Text>
        <ChevronDown size={20} color={Colors.light.gray} />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsModalVisible(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{label}</Text>
            </View>
            
            <FlatList
              data={options}
              keyExtractor={(item) => item.toString()}
              renderItem={({ item }) => {
                const itemStr = typeof item === 'number' && item % 1 !== 0 
                  ? item.toFixed(1) 
                  : item.toString();
                const valueStr = value || "";
                
                return (
                  <TouchableOpacity
                    style={[
                      styles.option,
                      valueStr === itemStr && styles.selectedOption
                    ]}
                    onPress={() => handleSelect(item)}
                  >
                    <Text style={[
                      styles.optionText,
                      valueStr === itemStr && styles.selectedOptionText
                    ]}>
                      {itemStr}
                    </Text>
                    {valueStr === itemStr && (
                      <Check size={20} color={Colors.light.primary} />
                    )}
                  </TouchableOpacity>
                );
              }}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 8,
  },
  required: {
    color: Colors.light.error,
  },
  selector: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  selectorText: {
    fontSize: 16,
    color: Colors.light.text,
    flex: 1,
  },
  placeholderText: {
    color: Colors.light.gray,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "70%",
  },
  modalHeader: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.light.text,
    textAlign: "center",
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
  },
  selectedOption: {
    backgroundColor: Colors.light.primary + "10",
  },
  optionText: {
    fontSize: 16,
    color: Colors.light.text,
  },
  selectedOptionText: {
    color: Colors.light.primary,
    fontWeight: "500",
  },
  separator: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginHorizontal: 16,
  },
  indicator: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.primary,
  },
});