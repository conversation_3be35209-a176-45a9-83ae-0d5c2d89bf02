import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Animated,
  Vibration,
  Alert,
  TextInput,
} from 'react-native';
import {
  ChevronRight,
  ChevronLeft,
  Check,
  Clock,
  Beaker,
  Palette,
  Timer as TimerIcon,
  Trophy,
  Play,
  Pause,
  RotateCcw,
  AlertCircle,
  Volume2,
  VolumeX,
  Package,
  GitMerge,
  Target,
  Calculator,
  FlaskConical,
  Brush,
  Calendar,
  Lightbulb,
  Sparkles,
  Plus,
  Minus,
  Scissors,
  Target as TargetIcon,
} from 'lucide-react-native';
// import { LinearGradient } from 'expo-linear-gradient'; // Removed for consistency
import Colors from '@/constants/colors';
import { VisualFormulationData } from '@/types/visual-formulation';
import * as Haptics from 'expo-haptics';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Custom animated touchable for micro-animations
const AnimatedTouchable = ({ children, onPress, style, ...props }: any) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  
  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.97,
      useNativeDriver: true,
      speed: 20,
      bounciness: 0,
    }).start();
  };
  
  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      speed: 20,
      bounciness: 0,
    }).start();
  };
  
  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
      {...props}
    >
      <Animated.View style={[style, { transform: [{ scale: scaleAnim }] }]}>
        {children}
      </Animated.View>
    </TouchableOpacity>
  );
};

// Gold/Mustard color palette for premium feel
const GoldColors = {
  primary: '#D4AF37', // Gold/Mustard primary
  primaryDark: '#B8941F', // Darker gold for depth
  primaryLight: '#E6C757', // Lighter gold for highlights
  secondary: '#8B6914', // Deep gold/bronze
  accent: '#CD853F', // Peru/warm accent
  text: '#2C2416', // Dark brown text
  surface: '#FFF8E7', // Warm cream surface
  success: '#7CB342', // Natural green
  warning: '#F9A825', // Amber warning
  error: '#D32F2F', // Standard error
  gray: '#8D7B68', // Warm gray
  lightGray: '#F5E6D3', // Warm light gray
};

interface InstructionsFlowProps {
  formulaData: VisualFormulationData;
  onClose?: () => void;
  onComplete?: () => void;
}

interface FlowStep {
  id: string;
  title: string;
  icon: any;
  color: string;
}

const FLOW_STEPS: FlowStep[] = [
  { id: 'checklist', title: 'Lista de Verificación', icon: Package, color: GoldColors.primary },
  { id: 'transformation', title: 'Tu Transformación', icon: GitMerge, color: GoldColors.accent },
  { id: 'formulas', title: 'Fórmulas Personalizadas', icon: Target, color: GoldColors.primary },
  { id: 'proportions', title: 'Guía de Proporciones', icon: Calculator, color: GoldColors.primaryDark },
  { id: 'calculator', title: 'Calculadora Visual', icon: Calculator, color: GoldColors.primary },
  { id: 'mixing', title: 'Estación de Mezcla', icon: FlaskConical, color: GoldColors.secondary },
  { id: 'application', title: 'Guía de Aplicación', icon: Brush, color: GoldColors.accent },
  { id: 'timeline', title: 'Cronograma', icon: Calendar, color: GoldColors.primaryDark },
  { id: 'tips', title: 'Tips Profesionales', icon: Lightbulb, color: GoldColors.warning },
  { id: 'result', title: 'Resultado Esperado', icon: Sparkles, color: GoldColors.success },
];

export default function InstructionsFlow({ formulaData, onClose, onComplete }: InstructionsFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [checkedItems, setCheckedItems] = useState<string[]>([]);
  const slideAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;
  
  // Progress dot animations
  const progressAnims = useRef(
    FLOW_STEPS.map(() => new Animated.Value(1))
  ).current;

  useEffect(() => {
    // Animate step transition
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: -50,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start(() => {
      slideAnim.setValue(50);
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    });
    
    // Animate progress dots
    progressAnims.forEach((anim, index) => {
      if (index === currentStep) {
        Animated.sequence([
          Animated.timing(anim, {
            toValue: 1.3,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(anim, {
            toValue: 1.2,
            duration: 100,
            useNativeDriver: true,
          }),
        ]).start();
      } else if (index < currentStep) {
        Animated.timing(anim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }).start();
      } else {
        Animated.timing(anim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }).start();
      }
    });
  }, [currentStep]);

  const goToNextStep = async () => {
    if (currentStep < FLOW_STEPS.length - 1) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setCompletedSteps([...completedSteps, currentStep]);
      setCurrentStep(currentStep + 1);
    } else {
      completeFlow();
    }
  };

  const goToPreviousStep = async () => {
    if (currentStep > 0) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setCurrentStep(currentStep - 1);
    }
  };

  const completeFlow = async () => {
    await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    setCompletedSteps([...completedSteps, currentStep]);
    if (onComplete) {
      onComplete();
    }
  };

  const renderStepContent = () => {
    const step = FLOW_STEPS[currentStep];
    
    switch (step.id) {
      case 'checklist':
        return <ChecklistScreen formulaData={formulaData} checkedItems={checkedItems} setCheckedItems={setCheckedItems} />;
      case 'transformation':
        return <TransformationScreen formulaData={formulaData} />;
      case 'formulas':
        return <FormulasScreen formulaData={formulaData} />;
      case 'proportions':
        return <ProportionsScreen formulaData={formulaData} />;
      case 'calculator':
        return <CalculatorScreen formulaData={formulaData} />;
      case 'mixing':
        return <MixingScreen formulaData={formulaData} />;
      case 'application':
        return <ApplicationScreen formulaData={formulaData} />;
      case 'timeline':
        return <TimelineScreen formulaData={formulaData} />;
      case 'tips':
        return <TipsScreen formulaData={formulaData} />;
      case 'result':
        return <ResultScreen formulaData={formulaData} />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: FLOW_STEPS[currentStep].color }]}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <ChevronLeft size={28} color="white" />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.stepNumber}>Paso {currentStep + 1} de {FLOW_STEPS.length}</Text>
          <Text style={styles.stepTitle}>{FLOW_STEPS[currentStep].title}</Text>
        </View>

        <View style={styles.soundButton} />
      </View>

      {/* Progress Indicators */}
      <View style={styles.progressContainer}>
        {/* Progress Bar */}
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarTrack} />
          <Animated.View 
            style={[
              styles.progressBarFill,
              { 
                width: `${((currentStep + 1) / FLOW_STEPS.length) * 100}%`,
                backgroundColor: FLOW_STEPS[currentStep].color
              }
            ]} 
          />
        </View>
        
        {/* Progress Dots */}
        <View style={styles.progressDotsContainer}>
          {FLOW_STEPS.map((step, index) => (
            <Animated.View
              key={step.id}
              style={[
                styles.progressDot,
                {
                  backgroundColor: index <= currentStep ? step.color : GoldColors.lightGray,
                  transform: [{ scale: progressAnims[index] }],
                  opacity: index <= currentStep ? 1 : 0.6,
                },
              ]}
            />
          ))}
        </View>
      </View>

      {/* Content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {renderStepContent()}
      </Animated.View>

      {/* Navigation */}
      <View style={styles.navigation}>
        <AnimatedTouchable
          style={[styles.navButton, styles.navButtonSecondary]}
          onPress={goToPreviousStep}
          disabled={currentStep === 0}
        >
          <ChevronLeft size={24} color={currentStep === 0 ? GoldColors.gray : GoldColors.text} />
          <Text style={[styles.navButtonText, currentStep === 0 && styles.navButtonTextDisabled]}>
            Anterior
          </Text>
        </AnimatedTouchable>

        <AnimatedTouchable
          style={[styles.navButton, styles.navButtonPrimary, { backgroundColor: FLOW_STEPS[currentStep].color }]}
          onPress={goToNextStep}
        >
          <Text style={styles.navButtonTextPrimary}>
            {currentStep === FLOW_STEPS.length - 1 ? 'Completar' : 'Siguiente'}
          </Text>
          <ChevronRight size={24} color="white" />
        </AnimatedTouchable>
      </View>
    </View>
  );
}

// Screen Components

// Pantalla 1: Lista de Verificación
function ChecklistScreen({ formulaData, checkedItems, setCheckedItems }: any) {
  // Crear datos mock completos si no vienen todos los productos
  const mockProducts = [
    { id: 'color-1', name: 'Illumina Color 8/69 (60g)', type: 'color', zone: 'General' },
    { id: 'color-2', name: 'Illumina Color 8/36 (30g)', type: 'color', zone: 'General' },
    { id: 'developer-1', name: 'Welloxon Perfect 20vol (135ml)', type: 'developer', zone: 'General' },
    { id: 'additive-1', name: 'Color Fresh 0/68 (5g)', type: 'additive', zone: 'General' },
  ];

  // Extraer TODOS los productos de la fórmula, incluyendo developers y additives
  let products = [];
  
  if (formulaData.zones && formulaData.zones.length > 0) {
    // Extraer productos de las zonas
    products = formulaData.zones.flatMap((zone: any) =>
      zone.ingredients.map((ing: any, index: number) => ({
        id: `${zone.zone}-${ing.name}-${index}`,
        name: `${ing.name} (${ing.amount}${ing.unit})`,
        type: ing.type || 'color',
        zone: zone.zone === 'roots' ? 'Raíces' : zone.zone === 'mids' ? 'Medios' : zone.zone === 'ends' ? 'Puntas' : 'General',
      }))
    );
    
    // Añadir developer si existe
    if (formulaData.mixingProportions?.developer) {
      const dev = formulaData.mixingProportions.developer;
      products.push({
        id: 'developer-main',
        name: `${dev.name} ${dev.volume}vol (${dev.amount || '135'}ml)`,
        type: 'developer',
        zone: 'General'
      });
    }
    
    // Añadir additives si existen
    if (formulaData.additives && formulaData.additives.length > 0) {
      formulaData.additives.forEach((add: any, index: number) => {
        products.push({
          id: `additive-${index}`,
          name: `${add.name} (${add.amount}${add.unit || 'g'})`,
          type: 'additive',
          zone: 'General'
        });
      });
    }
  }
  
  // Si no hay productos, usar mock
  if (products.length === 0) {
    products = mockProducts;
  }

  const materials = [
    { id: 'bowl', name: 'Bowl de mezcla', type: 'material' },
    { id: 'brush', name: 'Pincel aplicador', type: 'material' },
    { id: 'gloves', name: 'Guantes protectores', type: 'material' },
    { id: 'cape', name: 'Capa de cliente', type: 'material' },
    { id: 'towel', name: 'Toallas', type: 'material' },
  ];

  const toggleItem = async (id: string) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setCheckedItems((prev: string[]) =>
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  };
  
  // Calculate completion percentage
  const totalItems = products.length + materials.length;
  const completedItems = checkedItems.length;
  const completionPercentage = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

  const allItems = [...products, ...materials];
  const progress = (checkedItems.length / allItems.length) * 100;

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={[styles.checklistHeader, { backgroundColor: GoldColors.primary }]}>
        <Package size={48} color="white" />
        <Text style={styles.checklistHeaderTitle}>Verifica que tienes todo listo</Text>
        <View style={styles.progressBar}>
          <Animated.View 
            style={[
              styles.progressFill, 
              { width: `${progress}%`, backgroundColor: 'white' }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>{Math.round(progress)}% Completo</Text>
      </View>

      <View style={styles.checklistContent}>
        <Text style={styles.sectionTitle}>Productos de la Fórmula</Text>
        
        {/* Agrupar productos por tipo */}
        {['color', 'developer', 'additive'].map(type => {
          const productsOfType = products.filter((p: any) => p.type === type);
          if (productsOfType.length === 0) return null;
          
          const typeLabels = {
            color: 'Tintes',
            developer: 'Oxidantes',
            additive: 'Aditivos'
          };
          
          const typeColors = {
            color: GoldColors.primary,
            developer: GoldColors.accent,
            additive: GoldColors.warning
          };
          
          return (
            <View key={type} style={styles.productGroup}>
              <View style={styles.productGroupHeader}>
                <View style={[styles.productGroupIndicator, { backgroundColor: typeColors[type as keyof typeof typeColors] }]} />
                <Text style={styles.productGroupTitle}>{typeLabels[type as keyof typeof typeLabels]} ({productsOfType.length})</Text>
              </View>
              {productsOfType.map((item: any) => (
                <TouchableOpacity
                  key={item.id}
                  style={styles.checkItem}
                  onPress={() => toggleItem(item.id)}
                >
                  <View style={[
                    styles.checkbox,
                    checkedItems.includes(item.id) && styles.checkboxChecked
                  ]}>
                    {checkedItems.includes(item.id) && (
                      <Animated.View
                        style={{
                          transform: [{
                            scale: checkedItems.includes(item.id) ? 1 : 0
                          }]
                        }}
                      >
                        <Check size={16} color="white" />
                      </Animated.View>
                    )}
                  </View>
                  <View style={styles.checkItemInfo}>
                    <Text style={styles.checkItemName}>{item.name}</Text>
                    <Text style={styles.checkItemZone}>{item.zone}</Text>
                  </View>
                  <View style={[
                    styles.typeIndicator,
                    { backgroundColor: typeColors[type as keyof typeof typeColors] }
                  ]} />
                </TouchableOpacity>
              ))}
            </View>
          );
        })}

        <Text style={[styles.sectionTitle, { marginTop: 20 }]}>Materiales de Trabajo</Text>
        {materials.map(item => (
          <TouchableOpacity
            key={item.id}
            style={styles.checkItem}
            onPress={() => toggleItem(item.id)}
          >
            <View style={[
              styles.checkbox,
              checkedItems.includes(item.id) && styles.checkboxChecked
            ]}>
              {checkedItems.includes(item.id) && (
                <Animated.View
                  style={{
                    transform: [{
                      scale: checkedItems.includes(item.id) ? 1 : 0
                    }]
                  }}
                >
                  <Check size={16} color="white" />
                </Animated.View>
              )}
            </View>
            <Text style={styles.checkItemName}>{item.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
}

// Pantalla 2: Tu Transformación de Color
function TransformationScreen({ formulaData }: any) {
  const transition = formulaData.colorTransition;
  
  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.transformationHeader}>
        <View style={styles.transformationIconContainer}>
          <Sparkles size={48} color={GoldColors.primary} />
          <View style={styles.transformationIconOverlay}>
            <Palette size={24} color={GoldColors.accent} />
          </View>
        </View>
        <Text style={styles.transformationTitle}>Tu Transformación de Color</Text>
        <Text style={styles.transformationSubtitle}>
          Visualiza tu cambio personalizado
        </Text>
      </View>

      <View style={styles.colorComparison}>
        <View style={styles.colorBlock}>
          <View style={[styles.colorCircle, { backgroundColor: transition.current.hex || GoldColors.secondary }]}>
            <Text style={styles.colorLevel}>{transition.current.level}</Text>
          </View>
          <Text style={styles.colorLabel}>Color Actual</Text>
          <Text style={styles.colorTone}>{transition.current.tone}</Text>
        </View>

        <View style={styles.arrowContainer}>
          <ChevronRight size={32} color={GoldColors.accent} />
        </View>

        <View style={styles.colorBlock}>
          <View style={[styles.colorCircle, { backgroundColor: transition.target.hex || GoldColors.primaryLight }]}>
            <Text style={styles.colorLevel}>{transition.target.level}</Text>
          </View>
          <Text style={styles.colorLabel}>Color Objetivo</Text>
          <Text style={styles.colorTone}>{transition.target.tone}</Text>
        </View>
      </View>

      <View style={styles.difficultyCard}>
        <Text style={styles.difficultyLabel}>Nivel de Complejidad</Text>
        <View style={[
          styles.difficultyBadge,
          { backgroundColor: 
            transition.difficulty === 'easy' ? GoldColors.success :
            transition.difficulty === 'moderate' ? GoldColors.warning :
            transition.difficulty === 'challenging' ? GoldColors.primary :
            GoldColors.error
          }
        ]}>
          <Text style={styles.difficultyText}>
            {transition.difficulty === 'easy' ? 'Fácil' :
             transition.difficulty === 'moderate' ? 'Moderado' :
             transition.difficulty === 'challenging' ? 'Desafiante' :
             'Complejo'}
          </Text>
        </View>
      </View>

      {transition.sessions && transition.sessions > 1 && (
        <View style={styles.sessionsCard}>
          <AlertCircle size={20} color={GoldColors.warning} />
          <Text style={styles.sessionsText}>
            Se recomienda realizar en {transition.sessions} sesiones
          </Text>
        </View>
      )}

      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>{Math.abs(transition.target.level - transition.current.level).toFixed(0)}</Text>
          <Text style={styles.statLabel}>Niveles de cambio</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>{formulaData.applicationGuide.totalTime}</Text>
          <Text style={styles.statLabel}>Minutos totales</Text>
        </View>
      </View>
    </ScrollView>
  );
}

// Pantalla 3: Fórmulas Personalizadas
function FormulasScreen({ formulaData }: any) {
  const [selectedZone, setSelectedZone] = useState(0);
  const zones = formulaData.zones || [];
  
  // Detect if this is a single formula or multi-zone
  const isSingleFormula = zones.length === 1 && zones[0].zone === 'global';
  
  // Group ingredients by type to avoid duplicates
  const groupIngredientsByType = (ingredients: any[]) => {
    const groups: any = {
      color: [],
      developer: [],
      additive: [],
      treatment: []
    };
    
    ingredients.forEach(ing => {
      const type = ing.type || 'color';
      if (!groups[type]) groups[type] = [];
      groups[type].push(ing);
    });
    
    return groups;
  };

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.formulasHeader}>
        <Target size={48} color={GoldColors.primary} />
        <Text style={styles.formulasTitle}>
          {isSingleFormula ? 'Fórmula Personalizada' : 'Fórmulas por Zona'}
        </Text>
      </View>

      {!isSingleFormula && (
        <View style={styles.zoneSelector}>
          {zones.map((zone: any, index: number) => (
            <TouchableOpacity
              key={zone.zone}
              style={[
                styles.zoneTab,
                selectedZone === index && styles.zoneTabActive
              ]}
              onPress={() => setSelectedZone(index)}
            >
              <View style={styles.zoneTabContent}>
                <View style={[
                  styles.zoneTabIndicator,
                  { backgroundColor: 
                    zone.zone === 'roots' ? GoldColors.primary :
                    zone.zone === 'mids' ? GoldColors.warning :
                    zone.zone === 'ends' ? GoldColors.accent :
                    GoldColors.secondary
                  }
                ]} />
                <Text style={[
                  styles.zoneTabText,
                  selectedZone === index && styles.zoneTabTextActive
                ]}>
                  {zone.zone === 'roots' ? 'Raíces' :
                   zone.zone === 'mids' ? 'Medios' :
                   zone.zone === 'ends' ? 'Puntas' : 'General'}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {zones.length > 0 && (
        <View style={styles.formulaCard}>
          <Text style={styles.formulaZoneTitle}>
            {zones[selectedZone]?.title || 'Fórmula Principal'}
          </Text>
          
          {/* Group ingredients by type */}
          {(() => {
            const groups = groupIngredientsByType(zones[selectedZone]?.ingredients || []);
            const typeOrder = ['color', 'developer', 'additive', 'treatment'];
            const typeLabels: any = {
              color: 'Colorantes',
              developer: 'Oxidantes',
              additive: 'Aditivos',
              treatment: 'Tratamientos'
            };
            
            return typeOrder.map(type => {
              const ingredients = groups[type];
              if (!ingredients || ingredients.length === 0) return null;
              
              return (
                <View key={type} style={styles.ingredientGroup}>
                  <Text style={styles.ingredientGroupTitle}>{typeLabels[type]}</Text>
                  {ingredients.map((ing: any, index: number) => (
                    <View key={`${type}-${index}`} style={styles.ingredientRow}>
                      <View style={[
                        styles.ingredientIcon,
                        { backgroundColor: 
                          ing.type === 'color' ? GoldColors.primary :
                          ing.type === 'developer' ? GoldColors.accent :
                          ing.type === 'additive' ? GoldColors.warning :
                          GoldColors.secondary
                        }
                      ]}>
                        <Text style={styles.ingredientIconText}>
                          {ing.type === 'color' ? 'T' :
                           ing.type === 'developer' ? 'O' :
                           ing.type === 'additive' ? 'A' : 'T'}
                        </Text>
                      </View>
                      <View style={styles.ingredientInfo}>
                        <Text style={styles.ingredientName}>{ing.name}</Text>
                        {ing.code && <Text style={styles.ingredientCode}>Código: {ing.code}</Text>}
                      </View>
                      <View style={styles.ingredientAmountContainer}>
                        <Text style={styles.ingredientAmount}>{ing.amount}</Text>
                        <Text style={styles.ingredientUnit}>{ing.unit}</Text>
                      </View>
                    </View>
                  ))}
                </View>
              );
            });
          })()}

          {zones[selectedZone]?.mixingRatio && (
            <View style={styles.ratioInfo}>
              <Text style={styles.ratioLabel}>Proporción de mezcla:</Text>
              <Text style={styles.ratioValue}>{zones[selectedZone].mixingRatio}</Text>
            </View>
          )}

          {zones[selectedZone]?.processingTime && (
            <View style={styles.timeInfo}>
              <Clock size={16} color={GoldColors.secondary} />
              <Text style={styles.timeText}>
                Tiempo de proceso: {zones[selectedZone].processingTime} min
              </Text>
            </View>
          )}
        </View>
      )}
    </ScrollView>
  );
}

// Pantalla 4: Guía de Proporciones
function ProportionsScreen({ formulaData }: any) {
  const proportions = formulaData.mixingProportions;
  const [selectedRatio, setSelectedRatio] = useState(proportions.ratio);

  const calculateAmounts = (base: number, ratio: string) => {
    const ratioNum = parseFloat(ratio.split(':')[1]);
    return {
      color: base,
      developer: base * ratioNum,
      total: base + (base * ratioNum)
    };
  };

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.proportionsHeader}>
        <Calculator size={48} color={GoldColors.primaryDark} />
        <Text style={styles.proportionsTitle}>Guía de Proporciones</Text>
      </View>

      <View style={styles.ratioSelector}>
        {proportions.presets.map((preset: any) => (
          <TouchableOpacity
            key={preset.ratio}
            style={[
              styles.ratioOption,
              selectedRatio === preset.ratio && styles.ratioOptionActive
            ]}
            onPress={() => setSelectedRatio(preset.ratio)}
          >
            <Text style={[
              styles.ratioText,
              selectedRatio === preset.ratio && styles.ratioTextActive
            ]}>
              {preset.ratio}
            </Text>
            <Text style={[
              styles.ratioDescription,
              selectedRatio === preset.ratio && styles.ratioDescriptionActive
            ]}>
              {preset.description}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.visualProportions}>
        <View style={styles.proportionBars}>
          <View style={[styles.proportionBar, { flex: 1, backgroundColor: GoldColors.primary }]}>
            <Text style={styles.proportionBarText}>Tinte</Text>
          </View>
          <View style={[
            styles.proportionBar, 
            { 
              flex: parseFloat(selectedRatio.split(':')[1]), 
              backgroundColor: GoldColors.accent 
            }
          ]}>
            <Text style={styles.proportionBarText}>Oxidante</Text>
          </View>
        </View>
      </View>

      <View style={styles.referenceTable}>
        <Text style={styles.tableTitle}>Tabla de Referencia Rápida</Text>
        <View style={styles.tableHeader}>
          <Text style={styles.tableHeaderCell}>Tinte</Text>
          <Text style={styles.tableHeaderCell}>→</Text>
          <Text style={styles.tableHeaderCell}>Oxidante</Text>
          <Text style={styles.tableHeaderCell}>Total</Text>
        </View>
        {[30, 45, 60, 90].map(amount => {
          const calc = calculateAmounts(amount, selectedRatio);
          return (
            <View key={amount} style={styles.tableRow}>
              <Text style={styles.tableCell}>{calc.color}g</Text>
              <Text style={styles.tableCell}>→</Text>
              <Text style={styles.tableCell}>{calc.developer.toFixed(0)}g</Text>
              <Text style={styles.tableCell}>{calc.total.toFixed(0)}g</Text>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
}

// Pantalla 5: Calculadora Visual
function CalculatorScreen({ formulaData }: any) {
  const [hairLength, setHairLength] = useState('medium');
  const [baseAmount, setBaseAmount] = useState('60');
  
  const multipliers = {
    short: 0.8,
    medium: 1,
    long: 1.5,
    'extra-long': 2,
  };

  const calculateTotal = () => {
    const base = parseFloat(baseAmount) || 0;
    const multiplier = multipliers[hairLength as keyof typeof multipliers];
    const ratioNum = parseFloat(formulaData.mixingProportions.ratio.split(':')[1]);
    
    const colorAmount = base * multiplier;
    const developerAmount = colorAmount * ratioNum;
    
    return {
      color: colorAmount,
      developer: developerAmount,
      total: colorAmount + developerAmount
    };
  };

  const amounts = calculateTotal();

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.calculatorHeader}>
        <Calculator size={48} color={GoldColors.primary} />
        <Text style={styles.calculatorTitle}>Calculadora Visual</Text>
      </View>

      <View style={styles.hairLengthSelector}>
        <Text style={styles.selectorTitle}>Largo del Cabello</Text>
        <View style={styles.lengthOptions}>
          {Object.entries({
            short: { label: 'Corto', height: 40 },
            medium: { label: 'Medio', height: 55 },
            long: { label: 'Largo', height: 70 },
            'extra-long': { label: 'Extra Largo', height: 85 }
          }).map(([key, { label, height }]) => (
            <TouchableOpacity
              key={key}
              style={[
                styles.lengthOption,
                hairLength === key && styles.lengthOptionActive
              ]}
              onPress={() => setHairLength(key)}
            >
              <Scissors size={24} color={hairLength === key ? GoldColors.primary : GoldColors.gray} />
              <View style={[styles.hairSilhouette, { height, backgroundColor: hairLength === key ? GoldColors.primary : GoldColors.gray }]} />
              <Text style={[
                styles.lengthText,
                hairLength === key && styles.lengthTextActive
              ]}>
                {label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.amountInput}>
        <Text style={styles.inputLabel}>Cantidad Base (gramos)</Text>
        <View style={styles.inputRow}>
          <TouchableOpacity
            style={styles.adjustButton}
            onPress={() => setBaseAmount(String(Math.max(0, parseInt(baseAmount) - 5)))}
          >
            <Minus size={20} color={GoldColors.secondary} />
          </TouchableOpacity>
          <TextInput
            style={styles.amountInputField}
            value={baseAmount}
            onChangeText={setBaseAmount}
            keyboardType="numeric"
            placeholder="60"
          />
          <TouchableOpacity
            style={styles.adjustButton}
            onPress={() => setBaseAmount(String(parseInt(baseAmount) + 5))}
          >
            <Plus size={20} color={GoldColors.secondary} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.calculationResult}>
        <View style={styles.resultRow}>
          <View style={[styles.resultIcon, { backgroundColor: GoldColors.primary }]} />
          <Text style={styles.resultLabel}>Tinte:</Text>
          <Text style={styles.resultValue}>{amounts.color.toFixed(0)}g</Text>
        </View>
        <View style={styles.resultRow}>
          <View style={[styles.resultIcon, { backgroundColor: GoldColors.accent }]} />
          <Text style={styles.resultLabel}>Oxidante:</Text>
          <Text style={styles.resultValue}>{amounts.developer.toFixed(0)}g</Text>
        </View>
        <View style={[styles.resultRow, styles.totalRow]}>
          <View style={[styles.resultIcon, { backgroundColor: GoldColors.secondary }]} />
          <Text style={[styles.resultLabel, styles.totalLabel]}>Total:</Text>
          <Text style={[styles.resultValue, styles.totalValue]}>{amounts.total.toFixed(0)}g</Text>
        </View>
      </View>

      <View style={styles.visualRepresentation}>
        <View style={styles.bowlVisualization}>
          <View style={[styles.bowlContent, { height: `${Math.min(100, (amounts.total / 200) * 100)}%` }]}>
            <View style={[styles.colorLayer, { flex: amounts.color }]} />
            <View style={[styles.developerLayer, { flex: amounts.developer }]} />
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

// Pantalla 6: Estación de Mezcla (mejorada)
function MixingScreen({ formulaData }: any) {
  const [currentStep, setCurrentStep] = useState(0);
  const animValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animValue, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(animValue, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const mixingSteps = [
    { id: 1, text: 'Verter el tinte en el bowl', icon: '🎨' },
    { id: 2, text: 'Añadir el oxidante gradualmente', icon: '💧' },
    { id: 3, text: 'Mezclar con movimientos envolventes', icon: '🔄' },
    { id: 4, text: 'Verificar consistencia homogénea', icon: '✓' },
  ];

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.mixingHeader}>
        <FlaskConical size={48} color={GoldColors.primary} />
        <Text style={styles.mixingTitle}>Estación de Mezcla Profesional</Text>
      </View>

      <View style={styles.mixingAnimation}>
        <View style={styles.bowlContainer}>
          <View style={styles.bowl}>
            {/* Mixture content */}
            <Animated.View
              style={[
                styles.mixture,
                {
                  transform: [{
                    rotate: animValue.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '5deg'],
                    })
                  }],
                },
              ]}
            >
              <View style={[styles.mixtureGradient, { backgroundColor: GoldColors.primary }]} />
              <View style={[styles.colorSwirl, { 
                position: 'absolute',
                width: 60,
                height: 60,
                borderRadius: 30,
                backgroundColor: GoldColors.primaryLight,
                opacity: 0.5,
                top: '20%',
                left: '10%'
              }]} />
              <View style={[styles.colorSwirl, { 
                position: 'absolute',
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: GoldColors.accent,
                opacity: 0.3,
                bottom: '30%',
                right: '20%'
              }]} />
            </Animated.View>
            
            {/* Mixing brush visual */}
            <Animated.View
              style={[
                styles.mixingBrush,
                {
                  position: 'absolute',
                  transform: [{
                    rotate: animValue.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['-15deg', '15deg'],
                    })
                  }],
                },
              ]}
            >
              <Brush size={32} color={GoldColors.text} style={{ opacity: 0.7 }} />
            </Animated.View>
          </View>
          <Text style={styles.bowlLabel}>Mezcla cremosa y homogénea</Text>
        </View>
      </View>

      <View style={styles.stepsContainer}>
        {mixingSteps.map((step, index) => (
          <TouchableOpacity
            key={step.id}
            style={[
              styles.mixStep,
              index <= currentStep && styles.mixStepActive
            ]}
            onPress={() => setCurrentStep(index)}
          >
            <View style={[
              styles.stepIcon,
              index <= currentStep && styles.stepIconActive
            ]}>
              <Text style={styles.stepEmoji}>{step.icon}</Text>
            </View>
            <Text style={[
              styles.stepText,
              index <= currentStep && styles.stepTextActive
            ]}>
              {step.text}
            </Text>
            {index < currentStep && (
              <Check size={20} color={GoldColors.success} style={styles.stepCheck} />
            )}
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.mixtureInfo}>
        <Text style={styles.mixtureTitle}>Consistencia Ideal</Text>
        <Text style={styles.mixtureDescription}>
          La mezcla debe tener una textura cremosa similar al yogur bebible. 
          No debe ser ni muy líquida ni muy espesa.
        </Text>
      </View>
    </ScrollView>
  );
}

// Pantalla 7: Guía de Aplicación
function ApplicationScreen({ formulaData }: any) {
  const [selectedZone, setSelectedZone] = useState(0);
  const zones = formulaData.applicationGuide.zones;
  const technique = formulaData.applicationGuide.technique || 'full_color';

  // Professional application techniques based on service type
  const applicationTechniques: any = {
    full_color: [
      { id: 1, title: 'División del cabello', description: 'Divide en 4 secciones: 2 superiores y 2 inferiores. Usa clips para mantener separadas.', icon: Scissors },
      { id: 2, title: 'Aplicación en nuca', description: 'Comienza por la nuca con secciones de 0.5cm. Aplica de raíz a punta con pincel a 45°.', icon: Brush },
      { id: 3, title: 'Laterales', description: 'Continúa con secciones horizontales. Satura bien sin tocar el cuero cabelludo (2mm).', icon: Target },
      { id: 4, title: 'Corona', description: 'Finaliza con la corona. Aplica más rápido aquí ya que procesa más lento.', icon: Sparkles },
      { id: 5, title: 'Emulsión', description: 'Masajea suavemente con las yemas para distribuir uniformemente.', icon: RotateCcw }
    ],
    zonal: [
      { id: 1, title: 'Preparación por zonas', description: 'Separa claramente raíces (0-3cm), medios y puntas con clips.', icon: GitMerge },
      { id: 2, title: 'Aplicación raíces', description: 'Aplica perpendicular al cuero cabelludo. No superponer con color anterior.', icon: Target },
      { id: 3, title: 'Transición medios', description: 'Usa técnica de barrido diagonal para difuminar la unión.', icon: Brush },
      { id: 4, title: 'Puntas porosas', description: 'Aplica con técnica envolvente. Mayor saturación si están dañadas.', icon: Beaker },
      { id: 5, title: 'Sellado final', description: 'Peina suavemente para distribuir y sellar cutículas.', icon: Check }
    ],
    balayage: [
      { id: 1, title: 'Secciones en V', description: 'Crea secciones triangulares desde la coronilla.', icon: GitMerge },
      { id: 2, title: 'Técnica de pintado', description: 'Aplica con pincel plano en movimientos ascendentes suaves.', icon: Brush },
      { id: 3, title: 'Degradado natural', description: 'Más producto en puntas, menos en medios. Evita raíces.', icon: Sparkles },
      { id: 4, title: 'Papel separador', description: 'Usa papel de algodón para aislar secciones sin calor.', icon: Package },
      { id: 5, title: 'Difuminado', description: 'Usa los dedos para suavizar transiciones.', icon: RotateCcw }
    ],
    highlights: [
      { id: 1, title: 'Selección de mechones', description: 'Toma secciones finas de 0.3cm con peine de cola.', icon: Scissors },
      { id: 2, title: 'Aplicación en papel', description: 'Coloca sobre papel aluminio, aplica producto y dobla.', icon: Package },
      { id: 3, title: 'Técnica zigzag', description: 'Alterna mechones para resultado natural.', icon: GitMerge },
      { id: 4, title: 'Saturación completa', description: 'Asegura cobertura total dentro del papel.', icon: Beaker },
      { id: 5, title: 'Control de calor', description: 'Evita fuentes de calor directo sobre el aluminio.', icon: AlertCircle }
    ]
  };

  const currentTechniques = applicationTechniques[technique] || applicationTechniques.full_color;

  const getZoneDiagram = (zoneId: string) => {
    const diagrams: any = {
      roots: { top: '5%', height: '28%', backgroundColor: GoldColors.primary },
      mids: { top: '35%', height: '30%', backgroundColor: GoldColors.warning },
      ends: { top: '67%', height: '28%', backgroundColor: GoldColors.accent },
      crown: { top: '5%', left: '25%', right: '25%', height: '30%', backgroundColor: GoldColors.secondary, borderRadius: 100 },
      nape: { top: '70%', left: '20%', right: '20%', height: '25%', backgroundColor: GoldColors.primaryDark },
    };
    return diagrams[zoneId] || { top: '5%', height: '20%', backgroundColor: '#gray' };
  };

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.applicationHeader}>
        <Brush size={48} color={GoldColors.warning} />
        <Text style={styles.applicationTitle}>Guía de Aplicación Profesional</Text>
        <Text style={styles.applicationSubtitle}>
          {technique === 'zonal' ? 'Aplicación por Zonas' :
           technique === 'balayage' ? 'Técnica Balayage' :
           technique === 'highlights' ? 'Mechas con Papel' :
           'Color Completo'}
        </Text>
      </View>

      <View style={styles.headDiagram}>
        <View style={styles.headShape}>
          {/* Hair section divisions */}
          <View style={styles.sectionLines}>
            <View style={[styles.sectionLine, styles.horizontalLine]} />
            <View style={[styles.sectionLine, styles.verticalLine]} />
          </View>
          
          {zones.map((zone: any, index: number) => (
            <TouchableOpacity
              key={zone.id}
              style={[
                styles.zoneArea,
                getZoneDiagram(zone.id),
                selectedZone === index && styles.zoneAreaActive
              ]}
              onPress={() => setSelectedZone(index)}
            >
              <Text style={styles.zoneNumber}>{zone.order}</Text>
              {selectedZone === index && (
                <View style={styles.zoneArrows}>
                  {technique === 'full_color' && <ChevronRight size={16} color="white" style={styles.arrowIcon} />}
                  {technique === 'zonal' && <ChevronLeft size={16} color="white" style={styles.arrowIcon} />}
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Application direction legend */}
        <View style={styles.directionLegend}>
          <View style={styles.legendItem}>
            <ChevronRight size={14} color={GoldColors.primary} />
            <Text style={styles.legendText}>Dirección aplicación</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.saturationDot, { opacity: 1 }]} />
            <Text style={styles.legendText}>100% saturación</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.saturationDot, { opacity: 0.6 }]} />
            <Text style={styles.legendText}>60% saturación</Text>
          </View>
        </View>
      </View>

      <View style={styles.zoneDetails}>
        <Text style={styles.zoneName}>{zones[selectedZone].name}</Text>
        <Text style={styles.zoneDescription}>{zones[selectedZone].description}</Text>
        
        {/* Professional Application Techniques */}
        <View style={styles.techniquesContainer}>
          <Text style={styles.techniquesTitle}>Técnicas Profesionales:</Text>
          {currentTechniques.map((tech: any) => {
            const Icon = tech.icon;
            return (
              <View key={tech.id} style={styles.techniqueCard}>
                <View style={styles.techniqueIcon}>
                  <Icon size={24} color={GoldColors.primary} />
                </View>
                <View style={styles.techniqueContent}>
                  <Text style={styles.techniqueTitle}>{tech.title}</Text>
                  <Text style={styles.techniqueDescription}>{tech.description}</Text>
                </View>
              </View>
            );
          })}
        </View>

        {/* Zone-specific steps if available */}
        {formulaData.applicationGuide.steps && formulaData.applicationGuide.steps.length > 0 && (
          <View style={styles.applicationSteps}>
            <Text style={styles.stepsTitle}>Pasos Específicos:</Text>
            {formulaData.applicationGuide.steps
              .filter((step: any) => !step.zone || step.zone === zones[selectedZone].id || step.zone === 'all')
              .map((step: any, index: number) => (
                <View key={index} style={styles.applicationStep}>
                  <View style={styles.stepNumber}>
                    <Text style={styles.stepNumberText}>{index + 1}</Text>
                  </View>
                  <Text style={styles.stepDescription}>{step.description}</Text>
                </View>
              ))}
          </View>
        )}

        {/* Professional Tips */}
        <View style={styles.applicationTips}>
          <View style={styles.tipCard}>
            <AlertCircle size={20} color={GoldColors.warning} />
            <Text style={styles.tipText}>
              {technique === 'zonal' ? 'Evita superponer color en zonas previamente teñidas' :
               technique === 'balayage' ? 'Mantén el pincel en ángulo de 45° para transiciones suaves' :
               technique === 'highlights' ? 'No presiones el papel aluminio para evitar marcas' :
               'Aplica primero en zonas de mayor resistencia (canas)'}
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

// Pantalla 8: Cronograma (sin timer)
function TimelineScreen({ formulaData }: any) {
  const timeline = formulaData.timeline;
  const totalTime = formulaData.applicationGuide.totalTime;

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.timelineHeader}>
        <Calendar size={48} color={GoldColors.accent} />
        <Text style={styles.timelineTitle}>Cronograma del Proceso</Text>
        <Text style={styles.totalTime}>Tiempo Total: {totalTime} minutos</Text>
      </View>

      <View style={styles.timelineContainer}>
        {timeline.map((phase: any, index: number) => (
          <View key={phase.id} style={styles.timelineItem}>
            <View style={styles.timelineMarker}>
              <View style={styles.timelineDot} />
              {index < timeline.length - 1 && <View style={styles.timelineLine} />}
            </View>
            
            <View style={styles.timelineContent}>
              <View style={styles.timelineCard}>
                <View style={styles.timelineCardHeader}>
                  <Text style={styles.phaseTitle}>{phase.label}</Text>
                  <Text style={styles.phaseDuration}>
                    {phase.startTime}-{phase.endTime} min
                  </Text>
                </View>
                <Text style={styles.phaseDescription}>{phase.description}</Text>
                <View style={styles.phaseZones}>
                  {phase.zones.map((zone: string) => (
                    <View key={zone} style={styles.zoneChip}>
                      <Text style={styles.zoneChipText}>
                        {zone === 'roots' ? 'Raíces' :
                         zone === 'mids' ? 'Medios' :
                         zone === 'ends' ? 'Puntas' : 'Todo'}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            </View>
          </View>
        ))}
      </View>

      <View style={styles.timelineNote}>
        <AlertCircle size={20} color={GoldColors.secondary} />
        <Text style={styles.timelineNoteText}>
          Los tiempos son aproximados. Ajusta según las condiciones del cabello.
        </Text>
      </View>
    </ScrollView>
  );
}

// Pantalla 9: Tips Profesionales
function TipsScreen({ formulaData }: any) {
  const tips = formulaData.tips;
  
  const additionalTips = [
    {
      id: 'room-temp',
      type: 'tip',
      title: 'Temperatura Ambiente',
      description: 'Mantén el salón entre 20-25°C para un procesamiento óptimo',
      icon: '🌡️'
    },
    {
      id: 'strand-test',
      type: 'warning',
      title: 'Prueba de Mechón',
      description: 'Siempre realiza una prueba antes de la aplicación completa',
      icon: '⚠️'
    },
    {
      id: 'timing',
      type: 'tip',
      title: 'Control de Tiempo',
      description: 'Revisa el proceso cada 10 minutos para evaluar el desarrollo',
      icon: '⏱️'
    },
    {
      id: 'porosity',
      type: 'info',
      title: 'Porosidad del Cabello',
      description: 'El cabello poroso procesa más rápido. Ajusta los tiempos',
      icon: 'ℹ️'
    }
  ];

  const allTips = [...tips, ...additionalTips];

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.tipsHeader}>
        <Lightbulb size={48} color={GoldColors.warning} />
        <Text style={styles.tipsTitle}>Tips Profesionales</Text>
      </View>

      <View style={styles.tipsContainer}>
        {allTips.map((tip: any) => (
          <View
            key={tip.id}
            style={[
              styles.tipCard,
              tip.type === 'warning' && styles.tipCardWarning,
              tip.type === 'info' && styles.tipCardInfo
            ]}
          >
            <Text style={styles.tipIcon}>{tip.icon}</Text>
            <View style={styles.tipContent}>
              <Text style={styles.tipTitle}>{tip.title}</Text>
              <Text style={styles.tipDescription}>{tip.description}</Text>
            </View>
          </View>
        ))}
      </View>

      <View style={styles.proTip}>
        <View style={[styles.proTipGradient, { backgroundColor: GoldColors.warning }]}>
          <Trophy size={24} color="white" />
          <Text style={styles.proTipText}>
            Consejo Pro: Aplica siempre de puntas a raíces en cabellos vírgenes
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

// Pantalla 10: Resultado Esperado
function ResultScreen({ formulaData }: any) {
  const result = formulaData.expectedResult || {
    description: 'Color uniforme y brillante',
    coverage: '100%',
    duration: '6-8 semanas',
    maintenance: ['Usar shampoo sin sulfatos', 'Tratamiento hidratante semanal']
  };

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 30 }}>
      <View style={[styles.resultHeroSection, { backgroundColor: GoldColors.success }]}>
        <Sparkles size={64} color="white" />
        <Text style={styles.resultHeroTitle}>¡Resultado Espectacular!</Text>
        <Text style={styles.resultHeroSubtitle}>Tu transformación está completa</Text>
      </View>

      <View style={styles.resultColorCard}>
        <Text style={styles.resultSectionTitle}>Transformación Completa</Text>
        
        {/* Before/After Comparison */}
        <View style={styles.colorTransformation}>
          <View style={styles.colorComparisonItem}>
            <Text style={styles.colorLabel}>Antes</Text>
            <View style={[styles.resultColorCircle, { 
              backgroundColor: formulaData.colorTransition?.current?.hex || '#8B4513',
              transform: [{ scale: 0.9 }]
            }]}>
              <Text style={styles.resultColorLevel}>
                {formulaData.colorTransition?.current?.level || '5'}
              </Text>
            </View>
          </View>
          
          <View style={styles.transformArrow}>
            <ChevronRight size={28} color={GoldColors.primary} />
          </View>
          
          <View style={styles.colorComparisonItem}>
            <Text style={styles.colorLabel}>Después</Text>
            <View style={[styles.resultColorCircle, { 
              backgroundColor: formulaData.colorTransition?.target?.hex || GoldColors.primaryLight 
            }]}>
              <Animated.View style={styles.resultColorInner}>
                <Text style={styles.resultColorLevel}>
                  {formulaData.colorTransition?.target?.level || '8'}
                </Text>
                <View style={styles.resultColorDivider} />
                <Text style={styles.resultColorTone}>
                  {formulaData.colorTransition?.target?.tone || '69'}
                </Text>
              </Animated.View>
            </View>
          </View>
        </View>
        
        <Text style={styles.resultColorDescription}>{result.description}</Text>
      </View>

      <View style={styles.resultMetrics}>
        <View style={styles.metricCard}>
          <View style={[styles.metricIcon, { backgroundColor: GoldColors.secondary }]}>
            <TargetIcon size={24} color="white" />
          </View>
          <Text style={styles.metricValue}>{result.coverage}</Text>
          <Text style={styles.metricLabel}>Cobertura de Canas</Text>
        </View>
        
        <View style={styles.metricCard}>
          <View style={[styles.metricIcon, { backgroundColor: GoldColors.accent }]}>
            <Calendar size={24} color="white" />
          </View>
          <Text style={styles.metricValue}>{result.duration}</Text>
          <Text style={styles.metricLabel}>Duración Estimada</Text>
        </View>
        
        <View style={styles.metricCard}>
          <View style={[styles.metricIcon, { backgroundColor: GoldColors.warning }]}>
            <Sparkles size={24} color="white" />
          </View>
          <Text style={styles.metricValue}>Alto</Text>
          <Text style={styles.metricLabel}>Brillo y Luminosidad</Text>
        </View>
      </View>

      <View style={styles.maintenanceCard}>
        <View style={styles.maintenanceHeader}>
          <View style={styles.maintenanceIcon}>
            <AlertCircle size={24} color={GoldColors.primary} />
          </View>
          <Text style={styles.maintenanceTitle}>Cuidados Post-Color</Text>
        </View>
        
        <View style={styles.maintenanceContent}>
          {result.maintenance.map((item: string, index: number) => (
            <View key={index} style={styles.maintenanceStep}>
              <View style={styles.maintenanceCheckbox}>
                <Check size={16} color="white" />
              </View>
              <Text style={styles.maintenanceText}>{item}</Text>
            </View>
          ))}
        </View>
        
        <View style={styles.maintenanceTip}>
          <Text style={styles.maintenanceTipText}>
            Programa tu próxima visita en 4-6 semanas para mantener el color vibrante
          </Text>
        </View>
      </View>

      <View style={styles.completionCard}>
        <View style={[styles.completionGradient, { backgroundColor: GoldColors.primary }]}>
          <Trophy size={40} color="white" />
          <Text style={styles.completionTitle}>¡Proceso Completado con Éxito!</Text>
          <Text style={styles.completionSubtitle}>
            Has realizado una coloración profesional de alta calidad
          </Text>
        </View>
      </View>

      {/* Summary Button */}
      <AnimatedTouchable 
        style={styles.summaryButton}
        onPress={() => {
          // TODO: Navigate to summary or share options
          console.log('Show formula summary');
        }}
      >
        <View style={styles.summaryButtonContent}>
          <Package size={24} color="white" />
          <Text style={styles.summaryButtonText}>Ver Resumen de la Fórmula</Text>
          <ChevronRight size={20} color="white" />
        </View>
      </AnimatedTouchable>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoldColors.lightGray,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  closeButton: {
    padding: 5,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  stepNumber: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 5,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  soundButton: {
    width: 34,
  },
  progressContainer: {
    paddingVertical: 20,
    paddingHorizontal: 30,
  },
  progressBarContainer: {
    height: 4,
    backgroundColor: GoldColors.lightGray,
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 16,
  },
  progressBarTrack: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: GoldColors.lightGray,
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  content: {
    flex: 1,
  },
  navigation: {
    flexDirection: 'row',
    padding: 20,
    gap: 10,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: GoldColors.lightGray,
  },
  navButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 12,
    gap: 8,
  },
  navButtonSecondary: {
    backgroundColor: GoldColors.lightGray,
  },
  navButtonPrimary: {
    backgroundColor: GoldColors.primary,
  },
  navButtonText: {
    fontSize: 16,
    color: GoldColors.text,
    fontWeight: '500',
  },
  navButtonTextDisabled: {
    color: GoldColors.gray,
  },
  navButtonTextPrimary: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600',
  },
  screenContent: {
    flex: 1,
    paddingBottom: 20,
  },

  // Checklist Screen Styles
  checklistHeader: {
    padding: 30,
    alignItems: 'center',
  },
  checklistHeaderTitle: {
    fontSize: 18,
    color: 'white',
    fontWeight: '600',
    marginTop: 15,
    marginBottom: 20,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    color: 'white',
    fontSize: 14,
    marginTop: 10,
  },
  checklistContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 15,
  },
  checkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: GoldColors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  checkboxChecked: {
    backgroundColor: GoldColors.success,
    borderColor: GoldColors.success,
  },
  checkItemInfo: {
    flex: 1,
  },
  checkItemName: {
    fontSize: 15,
    color: GoldColors.text,
    fontWeight: '500',
  },
  checkItemZone: {
    fontSize: 12,
    color: GoldColors.gray,
    marginTop: 2,
  },
  typeIndicator: {
    width: 8,
    height: 40,
    borderRadius: 4,
    marginLeft: 12,
  },
  productGroup: {
    marginBottom: 20,
  },
  productGroupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  productGroupIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  productGroupTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: GoldColors.gray,
    flex: 1,
  },
  productGroupCount: {
    fontSize: 14,
    fontWeight: '500',
    color: GoldColors.gray,
  },

  // Transformation Screen Styles
  transformationHeader: {
    alignItems: 'center',
    padding: 30,
  },
  transformationTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GoldColors.text,
    marginTop: 15,
  },
  transformationSubtitle: {
    fontSize: 14,
    color: GoldColors.gray,
    marginTop: 5,
  },
  transformationIconContainer: {
    position: 'relative',
    width: 80,
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  transformationIconOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 6,
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  colorComparison: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
    paddingHorizontal: 20,
    marginBottom: 30,
    width: '100%',
  },
  colorBlock: {
    alignItems: 'center',
  },
  colorCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 2,
  },
  colorLevel: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  colorLabel: {
    fontSize: 14,
    color: GoldColors.gray,
    marginTop: 10,
  },
  colorTone: {
    fontSize: 16,
    fontWeight: '500',
    color: GoldColors.text,
    marginTop: 5,
  },
  arrowContainer: {
    paddingHorizontal: 10,
  },
  difficultyCard: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 20,
  },
  difficultyLabel: {
    fontSize: 14,
    color: GoldColors.gray,
    marginBottom: 10,
  },
  difficultyBadge: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 12,
  },
  difficultyText: {
    color: 'white',
    fontWeight: '600',
  },
  sessionsCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoldColors.warning + '20',
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 10,
    marginBottom: 20,
  },
  sessionsText: {
    flex: 1,
    marginLeft: 10,
    color: GoldColors.secondary,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 15,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: GoldColors.primary,
  },
  statLabel: {
    fontSize: 12,
    color: GoldColors.gray,
    marginTop: 5,
  },

  // Formulas Screen Styles
  formulasHeader: {
    alignItems: 'center',
    padding: 30,
  },
  formulasTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GoldColors.text,
    marginTop: 15,
  },
  zoneSelector: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 10,
  },
  zoneTab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: GoldColors.lightGray,
    borderRadius: 12,
  },
  zoneTabActive: {
    backgroundColor: GoldColors.primary,
  },
  zoneTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: GoldColors.text,
  },
  zoneTabTextActive: {
    color: 'white',
  },
  formulaCard: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 8,
    elevation: 3,
  },
  formulaZoneTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 20,
  },
  ingredientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  ingredientIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  ingredientIconText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  ingredientInfo: {
    flex: 1,
  },
  ingredientName: {
    fontSize: 15,
    fontWeight: '500',
    color: GoldColors.text,
  },
  ingredientCode: {
    fontSize: 12,
    color: GoldColors.gray,
  },
  ingredientAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.primary,
  },
  ratioInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: GoldColors.lightGray,
  },
  ratioLabel: {
    fontSize: 14,
    color: GoldColors.gray,
  },
  ratioValue: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.text,
  },
  timeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    gap: 8,
  },
  timeText: {
    fontSize: 14,
    color: GoldColors.text,
  },
  ingredientGroup: {
    marginBottom: 20,
  },
  ingredientGroupTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: GoldColors.gray,
    marginBottom: 10,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  ingredientAmountContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 4,
  },
  ingredientUnit: {
    fontSize: 12,
    color: GoldColors.gray,
    fontWeight: '400',
  },
  zoneTabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  zoneTabIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },

  // Proportions Screen Styles
  proportionsHeader: {
    alignItems: 'center',
    padding: 30,
  },
  proportionsTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GoldColors.text,
    marginTop: 15,
  },
  ratioSelector: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  ratioOption: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 10,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  ratioOptionActive: {
    borderColor: GoldColors.primary,
    backgroundColor: 'rgba(102, 126, 234, 0.05)',
  },
  ratioText: {
    fontSize: 18,
    fontWeight: '600',
    color: GoldColors.text,
  },
  ratioTextActive: {
    color: GoldColors.primary,
  },
  ratioDescription: {
    fontSize: 12,
    color: GoldColors.gray,
    marginTop: 5,
  },
  ratioDescriptionActive: {
    color: GoldColors.primary,
  },
  visualProportions: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  proportionBars: {
    flexDirection: 'row',
    height: 60,
    borderRadius: 16,
    overflow: 'hidden',
    gap: 2,
  },
  proportionBar: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  proportionBarText: {
    color: 'white',
    fontWeight: '600',
  },
  referenceTable: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
  },
  tableTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 15,
    textAlign: 'center',
  },
  tableHeader: {
    flexDirection: 'row',
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: GoldColors.lightGray,
    marginBottom: 10,
  },
  tableHeaderCell: {
    flex: 1,
    fontSize: 12,
    fontWeight: '600',
    color: GoldColors.gray,
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  tableCell: {
    flex: 1,
    fontSize: 14,
    color: GoldColors.text,
    textAlign: 'center',
  },

  // Calculator Screen Styles
  calculatorHeader: {
    alignItems: 'center',
    padding: 30,
  },
  calculatorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GoldColors.text,
    marginTop: 15,
  },
  hairLengthSelector: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  selectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 15,
  },
  lengthOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  lengthOption: {
    alignItems: 'center',
    padding: 10,
  },
  lengthOptionActive: {
    transform: [{ scale: 1.1 }],
  },
  hairIcon: {
    width: 40,
    backgroundColor: GoldColors.lightGray,
    borderRadius: 12,
    marginBottom: 5,
  },
  lengthText: {
    fontSize: 12,
    color: GoldColors.gray,
  },
  lengthTextActive: {
    color: GoldColors.primary,
    fontWeight: '600',
  },
  amountInput: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  inputLabel: {
    fontSize: 14,
    color: GoldColors.gray,
    marginBottom: 10,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 15,
  },
  adjustButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 3,
  },
  amountInputField: {
    fontSize: 28,
    fontWeight: '600',
    color: GoldColors.text,
    textAlign: 'center',
    backgroundColor: 'white',
    paddingVertical: 10,
    paddingHorizontal: 30,
    borderRadius: 15,
    minWidth: 120,
  },
  calculationResult: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
  },
  resultRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  resultIcon: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 10,
  },
  resultLabel: {
    flex: 1,
    fontSize: 14,
    color: GoldColors.gray,
  },
  resultValue: {
    fontSize: 18,
    fontWeight: '600',
    color: GoldColors.text,
  },
  totalRow: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: GoldColors.lightGray,
  },
  totalLabel: {
    fontWeight: '600',
    color: GoldColors.text,
  },
  totalValue: {
    fontSize: 22,
    color: GoldColors.primary,
  },
  visualRepresentation: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  bowlVisualization: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: GoldColors.lightGray,
    borderWidth: 3,
    borderColor: GoldColors.lightGray,
    overflow: 'hidden',
    justifyContent: 'flex-end',
  },
  bowlContent: {
    width: '100%',
    borderBottomLeftRadius: 75,
    borderBottomRightRadius: 75,
    overflow: 'hidden',
  },
  colorLayer: {
    backgroundColor: GoldColors.primary,
  },
  developerLayer: {
    backgroundColor: GoldColors.accent,
  },

  // Mixing Screen Styles
  mixingHeader: {
    alignItems: 'center',
    padding: 30,
  },
  mixingTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GoldColors.text,
    marginTop: 15,
  },
  mixingAnimation: {
    alignItems: 'center',
    marginBottom: 30,
  },
  bowlContainer: {
    alignItems: 'center',
  },
  bowl: {
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: GoldColors.lightGray,
    borderWidth: 3,
    borderColor: GoldColors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  mixture: {
    width: 160,
    height: 160,
    borderRadius: 80,
    overflow: 'hidden',
  },
  mixtureGradient: {
    flex: 1,
  },
  bowlLabel: {
    marginTop: 15,
    fontSize: 14,
    color: GoldColors.gray,
    fontStyle: 'italic',
  },
  mixingBrush: {
    position: 'absolute',
    top: '40%',
    left: '40%',
  },
  colorSwirl: {
    position: 'absolute',
  },
  stepsContainer: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  mixStep: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 10,
    position: 'relative',
  },
  mixStepActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.05)',
    borderWidth: 1,
    borderColor: GoldColors.primary,
  },
  stepIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: GoldColors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  stepIconActive: {
    backgroundColor: GoldColors.primary,
  },
  stepEmoji: {
    fontSize: 20,
  },
  stepText: {
    flex: 1,
    fontSize: 15,
    color: GoldColors.gray,
  },
  stepTextActive: {
    color: GoldColors.text,
    fontWeight: '500',
  },
  stepCheck: {
    position: 'absolute',
    right: 15,
  },
  mixtureInfo: {
    backgroundColor: GoldColors.surface,
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
  },
  mixtureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 10,
  },
  mixtureDescription: {
    fontSize: 14,
    color: GoldColors.gray,
    lineHeight: 20,
  },

  // Application Screen Styles
  applicationHeader: {
    alignItems: 'center',
    padding: 30,
  },
  applicationTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GoldColors.text,
    marginTop: 15,
  },
  applicationSubtitle: {
    fontSize: 14,
    color: GoldColors.gray,
    marginTop: 5,
  },
  headDiagram: {
    alignItems: 'center',
    marginBottom: 30,
  },
  headShape: {
    width: 200,
    height: 250,
    backgroundColor: GoldColors.lightGray,
    borderRadius: 100,
    borderWidth: 3,
    borderColor: GoldColors.lightGray,
    position: 'relative',
    overflow: 'hidden',
  },
  zoneArea: {
    position: 'absolute',
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
    opacity: 0.7,
  },
  zoneAreaActive: {
    opacity: 1,
    zIndex: 10,
  },
  zoneNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  zoneArrows: {
    position: 'absolute',
    right: 10,
    flexDirection: 'row',
  },
  arrowIcon: {
    opacity: 0.8,
  },
  sectionLines: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  sectionLine: {
    position: 'absolute',
    backgroundColor: GoldColors.gray,
    opacity: 0.3,
  },
  horizontalLine: {
    top: '50%',
    left: 0,
    right: 0,
    height: 1,
  },
  verticalLine: {
    top: 0,
    bottom: 0,
    left: '50%',
    width: 1,
  },
  directionLegend: {
    marginTop: 15,
    paddingHorizontal: 20,
    gap: 10,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  legendText: {
    fontSize: 12,
    color: GoldColors.gray,
  },
  saturationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: GoldColors.primary,
  },
  zoneDetails: {
    paddingHorizontal: 20,
  },
  zoneName: {
    fontSize: 20,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 10,
  },
  zoneDescription: {
    fontSize: 14,
    color: GoldColors.gray,
    marginBottom: 20,
  },
  applicationSteps: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 15,
  },
  stepsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 15,
  },
  applicationStep: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  stepNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: GoldColors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  stepNumberText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
  },
  stepDescription: {
    flex: 1,
    fontSize: 14,
    color: GoldColors.text,
    lineHeight: 20,
  },
  techniquesContainer: {
    marginBottom: 20,
  },
  techniquesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 15,
  },
  techniqueCard: {
    flexDirection: 'row',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 1,
  },
  techniqueIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: GoldColors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  techniqueContent: {
    flex: 1,
  },
  techniqueTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 5,
  },
  techniqueDescription: {
    fontSize: 13,
    color: GoldColors.gray,
    lineHeight: 18,
  },
  applicationTips: {
    marginTop: 20,
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: GoldColors.warning + '20',
    padding: 20,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: GoldColors.warning,
    alignItems: 'center',
  },
  tipText: {
    flex: 1,
    marginLeft: 10,
    fontSize: 13,
    color: GoldColors.secondary,
    lineHeight: 18,
  },

  // Timeline Screen Styles
  timelineHeader: {
    alignItems: 'center',
    padding: 30,
  },
  timelineTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GoldColors.text,
    marginTop: 15,
  },
  totalTime: {
    fontSize: 16,
    color: GoldColors.gray,
    marginTop: 10,
  },
  timelineContainer: {
    paddingHorizontal: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  timelineMarker: {
    width: 40,
    alignItems: 'center',
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: GoldColors.primary,
    marginTop: 5,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: GoldColors.lightGray,
    marginTop: 5,
  },
  timelineContent: {
    flex: 1,
    paddingLeft: 10,
  },
  timelineCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 2,
  },
  timelineCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  phaseTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.text,
  },
  phaseDuration: {
    fontSize: 14,
    color: GoldColors.primary,
    fontWeight: '500',
  },
  phaseDescription: {
    fontSize: 14,
    color: GoldColors.gray,
    marginBottom: 10,
  },
  phaseZones: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 5,
  },
  zoneChip: {
    backgroundColor: GoldColors.lightGray,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  zoneChipText: {
    fontSize: 12,
    color: GoldColors.text,
  },
  timelineNote: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoldColors.surface,
    marginHorizontal: 20,
    marginTop: 20,
    padding: 20,
    borderRadius: 12,
  },
  timelineNoteText: {
    flex: 1,
    marginLeft: 10,
    fontSize: 14,
    color: GoldColors.gray,
  },

  // Tips Screen Styles
  tipsHeader: {
    alignItems: 'center',
    padding: 30,
  },
  tipsTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GoldColors.text,
    marginTop: 15,
  },
  tipsContainer: {
    paddingHorizontal: 20,
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 2,
  },
  tipCardWarning: {
    backgroundColor: GoldColors.warning + '20',
    borderWidth: 1,
    borderColor: GoldColors.warning,
  },
  tipCardInfo: {
    backgroundColor: GoldColors.accent + '20',
    borderWidth: 1,
    borderColor: GoldColors.accent,
  },
  tipIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 5,
  },
  tipDescription: {
    fontSize: 14,
    color: GoldColors.gray,
    lineHeight: 20,
  },
  proTip: {
    marginHorizontal: 20,
    marginTop: 20,
  },
  proTipGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 15,
    gap: 12,
  },
  proTipText: {
    flex: 1,
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },

  // Result Screen Styles
  resultHeader: {
    alignItems: 'center',
    padding: 30,
  },
  resultTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GoldColors.text,
    marginTop: 15,
  },
  resultPreview: {
    alignItems: 'center',
    marginBottom: 30,
  },
  resultGradient: {
    width: 200,
    height: 200,
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.03,
    shadowRadius: 10,
    elevation: 5,
  },
  resultCircle: {
    backgroundColor: 'white',
    width: 140,
    height: 140,
    borderRadius: 70,
    alignItems: 'center',
    justifyContent: 'center',
  },
  resultLevel: {
    fontSize: 36,
    fontWeight: 'bold',
    color: GoldColors.text,
  },
  resultTone: {
    fontSize: 16,
    color: GoldColors.gray,
    marginTop: 5,
  },
  resultStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  statItem: {
    alignItems: 'center',
  },
  statIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  maintenanceSection: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
  },
  maintenanceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 15,
  },
  maintenanceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  maintenanceText: {
    flex: 1,
    marginLeft: 10,
    fontSize: 14,
    color: GoldColors.text,
  },
  successMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoldColors.success + '20',
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
    gap: 12,
  },
  successText: {
    flex: 1,
    fontSize: 16,
    color: GoldColors.success,
    fontWeight: '500',
  },
  
  // Calculator Screen - Additional Styles
  hairIconEmoji: {
    fontSize: 24,
    marginBottom: 5,
  },
  hairSilhouette: {
    width: 3,
    backgroundColor: GoldColors.primary,
    marginBottom: 5,
    borderRadius: 1.5,
  },
  
  // Mixing Screen - Additional Styles
  bowlContainer: {
    alignItems: 'center',
  },
  bowl: {
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: GoldColors.lightGray,
    borderWidth: 3,
    borderColor: GoldColors.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 2,
  },
  mixture: {
    width: 160,
    height: 160,
    borderRadius: 80,
    overflow: 'hidden',
  },
  mixtureGradient: {
    flex: 1,
  },
  mixtureBubbles: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bubble: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 50,
  },
  bubble1: {
    width: 20,
    height: 20,
    top: 20,
    left: 30,
  },
  bubble2: {
    width: 15,
    height: 15,
    top: 50,
    right: 25,
  },
  bubble3: {
    width: 10,
    height: 10,
    bottom: 30,
    left: 50,
  },
  bowlLabel: {
    marginTop: 15,
    fontSize: 14,
    color: GoldColors.gray,
    fontStyle: 'italic',
  },
  
  // Result Screen - New Design Styles
  resultHeroSection: {
    padding: 40,
    alignItems: 'center',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  resultHeroTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 15,
  },
  resultHeroSubtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    marginTop: 5,
  },
  resultColorCard: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginTop: -20,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.03,
    shadowRadius: 10,
    elevation: 5,
  },
  resultSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: GoldColors.text,
    marginBottom: 20,
  },
  resultColorDisplay: {
    marginBottom: 20,
  },
  resultColorCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: GoldColors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 2,
  },
  resultColorInner: {
    backgroundColor: 'white',
    paddingHorizontal: 30,
    paddingVertical: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  resultColorLevel: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoldColors.text,
  },
  resultColorDivider: {
    width: 40,
    height: 2,
    backgroundColor: GoldColors.gray,
    marginVertical: 8,
  },
  resultColorTone: {
    fontSize: 16,
    color: GoldColors.gray,
  },
  resultColorDescription: {
    fontSize: 16,
    color: GoldColors.text,
    textAlign: 'center',
  },
  colorTransformation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
    gap: 20,
  },
  colorComparisonItem: {
    alignItems: 'center',
    gap: 10,
  },
  colorLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: GoldColors.gray,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  transformArrow: {
    paddingHorizontal: 10,
  },
  resultMetrics: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginTop: 20,
    gap: 15,
  },
  metricCard: {
    flex: 1,
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
  },
  metricIcon: {
    width: 50,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  metricEmoji: {
    fontSize: 24,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoldColors.text,
    marginBottom: 5,
  },
  metricLabel: {
    fontSize: 11,
    color: GoldColors.gray,
    textAlign: 'center',
  },
  maintenanceCard: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginTop: 20,
    padding: 20,
    borderRadius: 15,
  },
  maintenanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  maintenanceIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  maintenanceContent: {
    gap: 12,
  },
  maintenanceStep: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  maintenanceCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: GoldColors.success,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  maintenanceTip: {
    backgroundColor: GoldColors.surface,
    padding: 20,
    borderRadius: 10,
    marginTop: 15,
  },
  maintenanceTipText: {
    fontSize: 14,
    color: GoldColors.text,
    lineHeight: 20,
  },
  completionCard: {
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },
  completionGradient: {
    padding: 30,
    alignItems: 'center',
  },
  completionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 15,
    textAlign: 'center',
  },
  completionSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.9)',
    marginTop: 8,
    textAlign: 'center',
  },
  summaryButton: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 30,
  },
  summaryButtonContent: {
    backgroundColor: GoldColors.secondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 18,
    borderRadius: 12,
    gap: 12,
  },
  summaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    flex: 1,
    textAlign: 'center',
  },
});