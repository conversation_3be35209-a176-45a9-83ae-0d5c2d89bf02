import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  StatusBar,
} from 'react-native';
import { ChevronLeft } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Colors from '@/constants/colors';
import { typography, spacing, layout } from '@/constants/theme';
import * as Haptics from 'expo-haptics';

interface BaseHeaderProps {
  title: string;
  subtitle?: string;
  onBack?: () => void;
  rightAction?: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'dark' | 'transparent';
  centerTitle?: boolean;
  hideBackButton?: boolean;
}

export const BaseHeader: React.FC<BaseHeaderProps> = ({
  title,
  subtitle,
  onBack,
  rightAction,
  variant = 'primary',
  centerTitle = false,
  hideBackButton = false,
}) => {
  const insets = useSafeAreaInsets();

  const handleBack = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onBack?.();
  };

  const backgroundColor = {
    primary: Colors.light.primary,
    secondary: Colors.light.secondary,
    dark: Colors.light.backgroundDark,
    transparent: 'transparent',
  }[variant];

  const textColor = 
    variant === 'transparent' 
      ? Colors.light.text 
      : Colors.light.textLight;

  const statusBarStyle = 
    variant === 'transparent' 
      ? 'dark-content' 
      : 'light-content';

  return (
    <>
      <StatusBar barStyle={statusBarStyle} />
      <View 
        style={[
          styles.container,
          { 
            backgroundColor,
            paddingTop: insets.top + spacing.sm,
          },
        ]}
      >
        <View style={styles.content}>
          {/* Left Section */}
          <View style={styles.left}>
            {onBack && !hideBackButton && (
              <TouchableOpacity
                onPress={handleBack}
                style={styles.backButton}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <ChevronLeft size={24} color={textColor} />
              </TouchableOpacity>
            )}
          </View>

          {/* Center Section */}
          <View style={[styles.center, centerTitle && styles.centerAbsolute]}>
            <Text 
              style={[styles.title, { color: textColor }]}
              numberOfLines={1}
            >
              {title}
            </Text>
            {subtitle && (
              <Text 
                style={[styles.subtitle, { color: textColor, opacity: 0.8 }]}
                numberOfLines={1}
              >
                {subtitle}
              </Text>
            )}
          </View>

          {/* Right Section */}
          <View style={styles.right}>
            {rightAction}
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    height: layout.headerHeight,
    justifyContent: 'flex-end',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.sm,
    height: 56,
  },
  left: {
    flex: 1,
    alignItems: 'flex-start',
  },
  center: {
    flex: 2,
    alignItems: 'center',
  },
  centerAbsolute: {
    position: 'absolute',
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  right: {
    flex: 1,
    alignItems: 'flex-end',
  },
  backButton: {
    padding: spacing.xs,
  },
  title: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
  },
  subtitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.regular,
    marginTop: 2,
  },
});