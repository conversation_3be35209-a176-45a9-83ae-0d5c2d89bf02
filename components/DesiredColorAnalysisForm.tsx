import React, { useState } from "react";
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, TextInput, Animated } from "react-native";
import { ChevronDown, ChevronUp, Check, Edit3 } from "lucide-react-native";
import Colors from "@/constants/colors";
import DiagnosisSelector from "./DiagnosisSelector";
import { HairZone, getNaturalToneOptions, getUndertoneOptions } from "@/types/hair-diagnosis";
import { COLOR_TECHNIQUES } from "@/types/desired-photo";
import { 
  DesiredColorAnalysisResult, 
  getContrastText, 
  getDirectionText, 
  getTextureText 
} from "@/types/desired-analysis";
import { 
  MaintenanceLevel, 
  AvoidableTone,
  BudgetLevel,
  getMaintenanceLevelLabel,
  getAvoidableToneLabel,
  getBudgetLevelLabel,
  getAvoidableToneOptions
} from "@/types/lifestyle-preferences";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import { 
  getRecommendedTechniques, 
  isTechniqueCompatible, 
  getTechniqueMismatchWarning,
  getTechniqueMaintenanceExplanation 
} from "@/utils/technique-recommendations";

interface DesiredColorAnalysisFormProps {
  analysisResult: DesiredColorAnalysisResult | null;
  onAnalysisChange: (analysis: DesiredColorAnalysisResult) => void;
  isFromAI?: boolean;
}

export default function DesiredColorAnalysisForm({
  analysisResult,
  onAnalysisChange,
  isFromAI = false
}: DesiredColorAnalysisFormProps) {
  const [expandedSection, setExpandedSection] = useState<'simple' | 'zones' | 'advanced'>('simple');
  const [currentZone, setCurrentZone] = useState<HairZone>(HairZone.ROOTS);
  const [showCustomTechnique, setShowCustomTechnique] = useState(false);
  const [showLifestyle, setShowLifestyle] = useState(true); // Show by default for logical flow
  const [techniqueWarning, setTechniqueWarning] = useState<string | null>(null);
  
  // Get salon configuration
  const { configuration } = useSalonConfigStore();
  
  // Animations
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const expandAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (analysisResult && isFromAI) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [analysisResult, isFromAI]);

  const toggleSection = (section: 'simple' | 'zones' | 'advanced') => {
    const isExpanding = expandedSection !== section;
    setExpandedSection(isExpanding ? section : 'simple');
    
    Animated.timing(expandAnim, {
      toValue: isExpanding ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const updateGeneral = (field: string, value: any) => {
    if (!analysisResult) return;
    
    // Check for technique compatibility when technique changes
    if (field === 'technique' && analysisResult.lifestyle?.maintenanceLevel) {
      const warning = getTechniqueMismatchWarning(value, analysisResult.lifestyle.maintenanceLevel);
      setTechniqueWarning(warning);
    }
    
    onAnalysisChange({
      ...analysisResult,
      general: { ...analysisResult.general, [field]: value },
      isFromAI: false
    });
  };

  const updateZone = (zone: HairZone, field: string, value: any) => {
    if (!analysisResult) return;
    onAnalysisChange({
      ...analysisResult,
      zones: {
        ...analysisResult.zones,
        [zone]: { ...analysisResult.zones[zone], [field]: value }
      },
      isFromAI: false
    });
  };

  const updateAdvanced = (field: string, value: any) => {
    if (!analysisResult) return;
    onAnalysisChange({
      ...analysisResult,
      advanced: { ...analysisResult.advanced, [field]: value },
      isFromAI: false
    });
  };

  const updateLifestyle = (field: string, value: any) => {
    if (!analysisResult) return;
    
    // Update technique warning when maintenance level changes
    if (field === 'maintenanceLevel' && analysisResult.general.technique) {
      const warning = getTechniqueMismatchWarning(analysisResult.general.technique, value);
      setTechniqueWarning(warning);
    }
    
    onAnalysisChange({
      ...analysisResult,
      lifestyle: { ...analysisResult.lifestyle, [field]: value } as any,
      isFromAI: false
    });
  };

  const toggleAvoidTone = (tone: AvoidableTone) => {
    if (!analysisResult) return;
    const currentTones = analysisResult.lifestyle?.avoidTones || [];
    const newTones = currentTones.includes(tone)
      ? currentTones.filter(t => t !== tone)
      : [...currentTones, tone];
    
    updateLifestyle('avoidTones', newTones);
  };

  if (!analysisResult) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>
          Captura o sube fotos del color deseado para comenzar el análisis
        </Text>
      </View>
    );
  }

  // Get recommended techniques based on maintenance level
  const recommendedTechniques = analysisResult?.lifestyle?.maintenanceLevel 
    ? getRecommendedTechniques(analysisResult.lifestyle.maintenanceLevel)
    : [];

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      {/* Lifestyle & Preferences Section FIRST */}
      <View style={styles.section}>
        <TouchableOpacity 
          style={styles.sectionHeader}
          onPress={() => setShowLifestyle(!showLifestyle)}
        >
          <Text style={styles.sectionTitle}>💫 Personalización y Estilo de Vida</Text>
          {showLifestyle ? 
            <ChevronUp size={20} color={Colors.light.primary} /> : 
            <ChevronDown size={20} color={Colors.light.primary} />
          }
        </TouchableOpacity>

        {showLifestyle && (
          <View style={styles.lifestyleContent}>
            {/* Maintenance Level */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Frecuencia de mantenimiento deseada</Text>
              <View style={styles.optionsRow}>
                {[MaintenanceLevel.LOW, MaintenanceLevel.MEDIUM, MaintenanceLevel.HIGH].map((level) => (
                  <TouchableOpacity
                    key={level}
                    style={[
                      styles.optionButton,
                      analysisResult.lifestyle?.maintenanceLevel === level && styles.optionButtonActive
                    ]}
                    onPress={() => updateLifestyle('maintenanceLevel', level)}
                  >
                    <Text style={[
                      styles.optionButtonText,
                      analysisResult.lifestyle?.maintenanceLevel === level && styles.optionButtonTextActive
                    ]}>
                      {getMaintenanceLevelLabel(level)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              <Text style={styles.helperText}>
                Te recomendaremos técnicas acordes a tu frecuencia preferida
              </Text>
            </View>

            {/* Tones to Avoid */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Tonos a evitar</Text>
              <View style={styles.tonesGrid}>
                {getAvoidableToneOptions().map((tone) => (
                  <TouchableOpacity
                    key={tone}
                    style={[
                      styles.toneChip,
                      analysisResult.lifestyle?.avoidTones?.includes(tone) && styles.toneChipActive
                    ]}
                    onPress={() => toggleAvoidTone(tone)}
                  >
                    <Text style={[
                      styles.toneChipText,
                      analysisResult.lifestyle?.avoidTones?.includes(tone) && styles.toneChipTextActive
                    ]}>
                      {getAvoidableToneLabel(tone)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Budget Level - Only if enabled in configuration */}
            {configuration.showBudgetOptions && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>Nivel de servicio preferido</Text>
                <View style={styles.optionsRow}>
                  {[BudgetLevel.ECO, BudgetLevel.STANDARD, BudgetLevel.PREMIUM].map((level) => (
                    <TouchableOpacity
                      key={level}
                      style={[
                        styles.optionButton,
                        analysisResult.lifestyle?.budgetLevel === level && styles.optionButtonActive
                      ]}
                      onPress={() => updateLifestyle('budgetLevel', level)}
                    >
                      <Text style={[
                        styles.optionButtonText,
                        analysisResult.lifestyle?.budgetLevel === level && styles.optionButtonTextActive
                      ]}>
                        {getBudgetLevelLabel(level)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
                <Text style={styles.helperText}>
                  Ajustaremos los productos y técnicas según tu preferencia
                </Text>
              </View>
            )}
          </View>
        )}
      </View>

      {/* Nivel 1 - Vista Simple */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>
            📊 Análisis del Color Deseado
            {isFromAI && <Text style={styles.aiIndicator}> •</Text>}
          </Text>
        </View>

        <View style={styles.simpleFields}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>
              Nivel general deseado
              {analysisResult.isFromAI && <Text style={styles.editIndicator}> •</Text>}
            </Text>
            <TextInput
              style={styles.input}
              value={analysisResult.general.overallLevel}
              onChangeText={(value) => updateGeneral('overallLevel', value)}
              placeholder="Ej: 8 o 8/9"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>
              Tono principal
              {analysisResult.isFromAI && <Text style={styles.editIndicator}> •</Text>}
            </Text>
            <TextInput
              style={styles.input}
              value={analysisResult.general.overallTone}
              onChangeText={(value) => updateGeneral('overallTone', value)}
              placeholder="Ej: Rubio ceniza"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>
              Técnica de aplicación
              {analysisResult.isFromAI && <Text style={styles.editIndicator}> •</Text>}
              {analysisResult.lifestyle?.maintenanceLevel && (
                <Text style={styles.labelHelper}> (basado en tu frecuencia de mantenimiento)</Text>
              )}
            </Text>
            
            {techniqueWarning && (
              <View style={styles.warningBox}>
                <Text style={styles.warningText}>{techniqueWarning}</Text>
              </View>
            )}
            
            <ScrollView style={styles.techniqueScrollContainer} showsVerticalScrollIndicator={false}>
              <View style={styles.techniqueGrid}>
              {COLOR_TECHNIQUES.map((technique) => {
                const isRecommended = recommendedTechniques.includes(technique.id);
                const maintenanceInfo = getTechniqueMaintenanceExplanation(technique.id);
                
                return (
                  <TouchableOpacity
                    key={technique.id}
                    style={[
                      styles.techniqueOption,
                      analysisResult.general.technique === technique.id && styles.techniqueOptionActive,
                      isRecommended && styles.techniqueOptionRecommended
                    ]}
                    onPress={() => updateGeneral('technique', technique.id)}
                  >
                    {isRecommended && (
                      <View style={styles.recommendedBadge}>
                        <Text style={styles.recommendedText}>Recomendado</Text>
                      </View>
                    )}
                    <Text style={styles.techniqueIcon}>{technique.icon}</Text>
                    <Text style={[
                      styles.techniqueName,
                      analysisResult.general.technique === technique.id && styles.techniqueNameActive
                    ]}>
                      {technique.name}
                    </Text>
                    {maintenanceInfo && (
                      <Text style={styles.techniqueDescription}>{maintenanceInfo}</Text>
                    )}
                  </TouchableOpacity>
                );
              })}
              <TouchableOpacity
                style={[
                  styles.techniqueOption,
                  showCustomTechnique && styles.techniqueOptionActive
                ]}
                onPress={() => setShowCustomTechnique(true)}
              >
                <Text style={styles.techniqueIcon}>➕</Text>
                <Text style={styles.techniqueName}>Custom</Text>
              </TouchableOpacity>
            </View>
            </ScrollView>
            
            {showCustomTechnique && (
              <TextInput
                style={[styles.input, styles.customTechniqueInput]}
                value={analysisResult.general.customTechnique || ''}
                onChangeText={(value) => updateGeneral('customTechnique', value)}
                placeholder="Ej: Balayage invertido con babylights"
                multiline
              />
            )}
          </View>
        </View>

        <TouchableOpacity 
          style={styles.expandButton}
          onPress={() => toggleSection('zones')}
        >
          <Text style={styles.expandButtonText}>
            {expandedSection === 'zones' ? 'Ocultar' : 'Ver'} análisis detallado
          </Text>
          {expandedSection === 'zones' ? 
            <ChevronUp size={16} color={Colors.light.primary} /> : 
            <ChevronDown size={16} color={Colors.light.primary} />
          }
        </TouchableOpacity>
      </View>

      {/* Nivel 2 - Análisis por Zonas */}
      {expandedSection === 'zones' && (
        <Animated.View style={[styles.section, styles.zonesSection]}>
          <Text style={styles.sectionTitle}>📍 Análisis por Zonas</Text>
          
          <View style={styles.zoneTabs}>
            {Object.values(HairZone).map((zone) => (
              <TouchableOpacity
                key={zone}
                style={[styles.zoneTab, currentZone === zone && styles.activeZoneTab]}
                onPress={() => setCurrentZone(zone)}
              >
                <Text style={[styles.zoneTabText, currentZone === zone && styles.activeZoneTabText]}>
                  {zone}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.zoneFields}>
            <DiagnosisSelector
              label="Nivel deseado"
              value={analysisResult.zones[currentZone]?.desiredLevel?.toString() || ""}
              options={Array.from({length: 10}, (_, i) => i + 1)}
              onValueChange={(value) => updateZone(currentZone, 'desiredLevel', parseInt(value))}
              required
              isFromAI={analysisResult.isFromAI}
            />

            <DiagnosisSelector
              label="Tono deseado"
              value={analysisResult.zones[currentZone]?.desiredTone || ""}
              options={getNaturalToneOptions()}
              onValueChange={(value) => updateZone(currentZone, 'desiredTone', value)}
              required
              isFromAI={analysisResult.isFromAI}
            />

            <DiagnosisSelector
              label="Subtono/Reflejo"
              value={analysisResult.zones[currentZone]?.desiredUndertone || ""}
              options={getUndertoneOptions()}
              onValueChange={(value) => updateZone(currentZone, 'desiredUndertone', value)}
              required
              isFromAI={analysisResult.isFromAI}
            />

            <View style={styles.formGroup}>
              <Text style={styles.label}>Cobertura en esta zona (%)</Text>
              <TextInput
                style={styles.input}
                value={analysisResult.zones[currentZone]?.coverage?.toString() || "100"}
                onChangeText={(value) => updateZone(currentZone, 'coverage', parseInt(value) || 100)}
                keyboardType="numeric"
                placeholder="0-100"
                maxLength={3}
              />
            </View>
          </View>

          <TouchableOpacity 
            style={styles.expandButton}
            onPress={() => toggleSection('advanced')}
          >
            <Text style={styles.expandButtonText}>
              {expandedSection === 'advanced' ? 'Ocultar' : 'Ver'} opciones avanzadas
            </Text>
            {expandedSection === 'advanced' ? 
              <ChevronUp size={16} color={Colors.light.primary} /> : 
              <ChevronDown size={16} color={Colors.light.primary} />
            }
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Nivel 3 - Detalles Avanzados */}
      {expandedSection === 'advanced' && (
        <Animated.View style={[styles.section, styles.advancedSection]}>
          <Text style={styles.sectionTitle}>⚙️ Detalles Técnicos</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Nivel de contraste</Text>
            <View style={styles.optionsRow}>
              {(['subtle', 'medium', 'high'] as const).map((level) => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.optionButton,
                    analysisResult.advanced.contrast === level && styles.optionButtonActive
                  ]}
                  onPress={() => updateAdvanced('contrast', level)}
                >
                  <Text style={[
                    styles.optionButtonText,
                    analysisResult.advanced.contrast === level && styles.optionButtonTextActive
                  ]}>
                    {getContrastText(level)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Dirección del color</Text>
            <View style={styles.optionsRow}>
              {(['warmer', 'neutral', 'cooler'] as const).map((dir) => (
                <TouchableOpacity
                  key={dir}
                  style={[
                    styles.optionButton,
                    analysisResult.advanced.direction === dir && styles.optionButtonActive
                  ]}
                  onPress={() => updateAdvanced('direction', dir)}
                >
                  <Text style={[
                    styles.optionButtonText,
                    analysisResult.advanced.direction === dir && styles.optionButtonTextActive
                  ]}>
                    {getDirectionText(dir)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Cobertura de canas (%)</Text>
            <TextInput
              style={styles.input}
              value={analysisResult.advanced.graysCoverage?.toString() || "100"}
              onChangeText={(value) => updateAdvanced('graysCoverage', parseInt(value) || 100)}
              keyboardType="numeric"
              placeholder="0-100"
              maxLength={3}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Textura final deseada</Text>
            <View style={styles.optionsRow}>
              {(['matte', 'natural', 'glossy'] as const).map((texture) => (
                <TouchableOpacity
                  key={texture}
                  style={[
                    styles.optionButton,
                    analysisResult.advanced.finalTexture === texture && styles.optionButtonActive
                  ]}
                  onPress={() => updateAdvanced('finalTexture', texture)}
                >
                  <Text style={[
                    styles.optionButtonText,
                    analysisResult.advanced.finalTexture === texture && styles.optionButtonTextActive
                  ]}>
                    {getTextureText(texture)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Notas especiales</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={analysisResult.advanced.specialNotes || ''}
              onChangeText={(value) => updateAdvanced('specialNotes', value)}
              placeholder="Consideraciones adicionales para la formulación..."
              multiline
              numberOfLines={3}
            />
          </View>
        </Animated.View>
      )}

    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  emptyContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 24,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 100,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.light.gray,
    textAlign: "center",
  },
  section: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  zonesSection: {
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  advancedSection: {
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.light.text,
  },
  aiIndicator: {
    color: Colors.light.primary,
    fontSize: 16,
  },
  editIndicator: {
    color: Colors.light.primary,
    fontSize: 14,
  },
  simpleFields: {
    gap: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 8,
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  techniqueScrollContainer: {
    maxHeight: 400,
    marginTop: 8,
  },
  techniqueGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
    paddingBottom: 12,
  },
  techniqueOption: {
    alignItems: "center",
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 12,
    width: '47%',
    borderWidth: 1,
    borderColor: Colors.light.border,
    position: 'relative',
    minHeight: 100,
  },
  techniqueOptionActive: {
    backgroundColor: Colors.light.primary + "10",
    borderColor: Colors.light.primary,
  },
  techniqueOptionRecommended: {
    borderColor: Colors.light.success,
    borderWidth: 2,
  },
  techniqueIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  techniqueName: {
    fontSize: 12,
    fontWeight: "500",
    color: Colors.light.gray,
    textAlign: "center",
  },
  techniqueNameActive: {
    color: Colors.light.primary,
    fontWeight: "600",
  },
  customTechniqueInput: {
    marginTop: 8,
    minHeight: 60,
  },
  expandButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    marginTop: 8,
    gap: 8,
  },
  expandButtonText: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: "500",
  },
  zoneTabs: {
    flexDirection: "row",
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    marginBottom: 16,
  },
  zoneTab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: "center",
  },
  activeZoneTab: {
    backgroundColor: Colors.light.primary + "10",
  },
  zoneTabText: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  activeZoneTabText: {
    color: Colors.light.primary,
    fontWeight: "600",
  },
  zoneFields: {
    gap: 16,
  },
  optionsRow: {
    flexDirection: "row",
    gap: 8,
    marginTop: 8,
  },
  optionButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 16,
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: "center",
  },
  optionButtonActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  optionButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.light.text,
  },
  optionButtonTextActive: {
    color: "white",
    fontWeight: "600",
  },
  lifestyleContent: {
    marginTop: 16,
  },
  tonesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginTop: 8,
  },
  toneChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.surface,
  },
  toneChipActive: {
    backgroundColor: Colors.light.error + "10",
    borderColor: Colors.light.error,
  },
  toneChipText: {
    fontSize: 14,
    color: Colors.light.text,
  },
  toneChipTextActive: {
    color: Colors.light.error,
    fontWeight: "600",
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 8,
    fontStyle: "italic",
  },
  labelHelper: {
    fontSize: 12,
    color: Colors.light.gray,
    fontWeight: "normal",
  },
  recommendedBadge: {
    position: "absolute",
    top: 4,
    right: 4,
    backgroundColor: Colors.light.success,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  recommendedText: {
    fontSize: 10,
    fontWeight: "600",
    color: "white",
  },
  techniqueDescription: {
    fontSize: 11,
    color: Colors.light.gray,
    textAlign: "center",
    marginTop: 4,
    paddingHorizontal: 4,
  },
  warningBox: {
    backgroundColor: Colors.light.warning + "10",
    borderColor: Colors.light.warning,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  warningText: {
    fontSize: 13,
    color: Colors.light.warning,
    lineHeight: 18,
  },
});