/**
 * Progressive Form Component
 * Componente principal para formularios con revelación progresiva
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Animated,
  Dimensions,
  Platform
} from 'react-native';
import { ChevronLeft, ChevronRight, Zap, Clock, CheckCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';
import BaseButton from '@/components/base/BaseButton';
import {
  SmartFormConfig,
  ProgressiveFormState,
  FieldConfiguration,
  AIConfidenceScore,
  ConsultationContext
} from '@/types/progressive-disclosure';
import { ProgressiveDisclosureService } from '@/services/progressiveDisclosureService';
import ProgressiveField from './ProgressiveField';
import LevelIndicator from './LevelIndicator';
import AIConfidenceIndicator from './AIConfidenceIndicator';

interface ProgressiveFormProps {
  formId: string;
  config: SmartFormConfig;
  context?: ConsultationContext;
  onFieldChange?: (fieldId: string, value: any) => void;
  onLevelComplete?: (level: number) => void;
  onFormComplete?: (data: Record<string, any>) => void;
  onAISuggestion?: (suggestions: Record<string, any>, confidence: AIConfidenceScore) => void;
  initialData?: Record<string, any>;
  showAIIndicators?: boolean;
  allowLevelSkipping?: boolean;
}

const { width: screenWidth } = Dimensions.get('window');

export default function ProgressiveForm({
  formId,
  config,
  context,
  onFieldChange,
  onLevelComplete,
  onFormComplete,
  onAISuggestion,
  initialData,
  showAIIndicators = true,
  allowLevelSkipping = false
}: ProgressiveFormProps) {
  const [formState, setFormState] = useState<ProgressiveFormState | null>(null);
  const [visibleFields, setVisibleFields] = useState<FieldConfiguration[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [aiConfidence, setAiConfidence] = useState<AIConfidenceScore | null>(null);
  
  // Animaciones
  const [slideAnim] = useState(new Animated.Value(0));
  const [fadeAnim] = useState(new Animated.Value(1));

  const service = ProgressiveDisclosureService.getInstance();

  // Inicializar formulario
  useEffect(() => {
    const initialState = service.initializeForm(formId, config, context);
    
    // Aplicar datos iniciales si existen
    if (initialData) {
      Object.entries(initialData).forEach(([fieldId, value]) => {
        service.updateField(formId, fieldId, value);
      });
    }
    
    setFormState(initialState);
    updateVisibleFields();
  }, [formId, config, context, initialData]);

  // Actualizar campos visibles cuando cambia el estado
  const updateVisibleFields = useCallback(() => {
    const fields = service.getVisibleFields(formId);
    setVisibleFields(fields);
  }, [formId]);

  // Manejar cambio de campo
  const handleFieldChange = useCallback((fieldId: string, value: any) => {
    const newState = service.updateField(formId, fieldId, value);
    setFormState({ ...newState });
    updateVisibleFields();
    
    if (onFieldChange) {
      onFieldChange(fieldId, value);
    }
  }, [formId, onFieldChange, updateVisibleFields]);

  // Manejar sugerencias de IA
  const handleAISuggestions = useCallback((
    suggestions: Record<string, any>, 
    confidence: AIConfidenceScore
  ) => {
    service.applyAISuggestions(formId, suggestions, confidence);
    setAiConfidence(confidence);
    
    const updatedState = service.getVisibleFields(formId);
    updateVisibleFields();
    
    if (onAISuggestion) {
      onAISuggestion(suggestions, confidence);
    }
  }, [formId, onAISuggestion, updateVisibleFields]);

  // Avanzar nivel
  const handleNextLevel = useCallback(() => {
    if (!formState?.canProceed) return;

    setIsLoading(true);
    
    // Animación de transición
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: -screenWidth,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      const success = service.advanceToNextLevel(formId);
      
      if (success) {
        const newState = { ...formState };
        newState.currentLevel = formState.currentLevel + 1;
        setFormState(newState);
        updateVisibleFields();
        
        if (onLevelComplete) {
          onLevelComplete(formState.currentLevel);
        }
      }
      
      // Resetear animaciones
      slideAnim.setValue(screenWidth);
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
      
      setIsLoading(false);
    });
  }, [formState, formId, onLevelComplete, updateVisibleFields, slideAnim, fadeAnim]);

  // Retroceder nivel
  const handlePreviousLevel = useCallback(() => {
    const success = service.goToPreviousLevel(formId);
    
    if (success && formState) {
      const newState = { ...formState };
      newState.currentLevel = formState.currentLevel - 1;
      setFormState(newState);
      updateVisibleFields();
    }
  }, [formId, formState, updateVisibleFields]);

  // Completar formulario
  const handleComplete = useCallback(() => {
    if (!formState?.isValid) return;

    const metrics = service.completeForm(formId);
    
    if (onFormComplete) {
      onFormComplete(formState.fieldValues);
    }
  }, [formId, formState, onFormComplete]);

  // Aceptar sugerencia de IA
  const handleAcceptAISuggestion = useCallback((fieldId: string) => {
    service.acceptAISuggestion(formId, fieldId);
    updateVisibleFields();
  }, [formId, updateVisibleFields]);

  // Rechazar sugerencia de IA
  const handleRejectAISuggestion = useCallback((fieldId: string) => {
    service.rejectAISuggestion(formId, fieldId);
    updateVisibleFields();
  }, [formId, updateVisibleFields]);

  if (!formState) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Inicializando formulario...</Text>
      </View>
    );
  }

  const currentLevelConfig = config.levels.find(l => l.priority === formState.currentLevel);
  const isLastLevel = formState.currentLevel === Math.max(...config.levels.map(l => l.priority));
  const canGoBack = formState.currentLevel > 1;

  return (
    <View style={styles.container}>
      {/* Header con progreso */}
      <View style={styles.header}>
        <LevelIndicator
          currentLevel={formState.currentLevel}
          completedLevels={formState.completedLevels}
          totalLevels={config.levels.length}
          levelConfig={currentLevelConfig}
        />
        
        {showAIIndicators && aiConfidence && (
          <AIConfidenceIndicator confidence={aiConfidence} />
        )}
      </View>

      {/* Contenido del formulario */}
      <Animated.View 
        style={[
          styles.content,
          {
            transform: [{ translateX: slideAnim }],
            opacity: fadeAnim,
          }
        ]}
      >
        <ScrollView 
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {currentLevelConfig && (
            <View style={styles.levelHeader}>
              <Text style={styles.levelTitle}>{currentLevelConfig.name}</Text>
              <Text style={styles.levelDescription}>{currentLevelConfig.description}</Text>
              
              {currentLevelConfig.estimatedTime > 0 && (
                <View style={styles.timeEstimate}>
                  <Clock size={16} color={Colors.light.textSecondary} />
                  <Text style={styles.timeText}>
                    ~{Math.ceil(currentLevelConfig.estimatedTime / 60)} min
                  </Text>
                </View>
              )}
            </View>
          )}

          {/* Campos del formulario */}
          <View style={styles.fieldsContainer}>
            {visibleFields.map((field, index) => (
              <ProgressiveField
                key={field.id}
                field={field}
                value={formState.fieldValues[field.id]}
                error={formState.validationErrors[field.id]}
                aiSuggestion={formState.aiSuggestions[field.id]}
                onChange={(value) => handleFieldChange(field.id, value)}
                onAcceptAISuggestion={() => handleAcceptAISuggestion(field.id)}
                onRejectAISuggestion={() => handleRejectAISuggestion(field.id)}
                isVisible={field.level <= formState.currentLevel}
                animationDelay={index * 100}
              />
            ))}
          </View>
        </ScrollView>
      </Animated.View>

      {/* Footer con navegación */}
      <View style={styles.footer}>
        <BaseButton
          title="Anterior"
          variant="outline"
          size="md"
          icon={ChevronLeft}
          iconPosition="left"
          onPress={handlePreviousLevel}
          disabled={!canGoBack || isLoading}
          style={[styles.navButton, !canGoBack && styles.navButtonDisabled]}
        />

        <BaseButton
          title={isLastLevel ? "Completar" : "Siguiente"}
          variant="primary"
          size="md"
          icon={isLastLevel ? CheckCircle : ChevronRight}
          iconPosition="right"
          onPress={isLastLevel ? handleComplete : handleNextLevel}
          disabled={!formState.canProceed || isLoading}
          loading={isLoading}
          style={styles.navButton}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    ...shadows.sm,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  levelHeader: {
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
    marginBottom: spacing.lg,
  },
  levelTitle: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  levelDescription: {
    fontSize: typography.sizes.md,
    color: Colors.light.textSecondary,
    lineHeight: 22,
    marginBottom: spacing.sm,
  },
  timeEstimate: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  timeText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  fieldsContainer: {
    gap: spacing.lg,
    paddingBottom: spacing.xl,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    ...shadows.sm,
  },
  navButton: {
    flex: 0.45,
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
});
