/**
 * Progressive Field Component
 * Componente para campos individuales en formularios progresivos
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Animated,
  Alert
} from 'react-native';
import { 
  AlertCircle, 
  CheckCircle, 
  Zap, 
  X, 
  Check,
  HelpCircle,
  Eye,
  EyeOff
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';
import { FieldConfiguration } from '@/types/progressive-disclosure';
import BaseButton from '@/components/base/BaseButton';
import DiagnosisSelector from '@/components/DiagnosisSelector';

interface ProgressiveFieldProps {
  field: FieldConfiguration;
  value: any;
  error?: string;
  aiSuggestion?: {
    value: any;
    confidence: number;
    applied: boolean;
  };
  onChange: (value: any) => void;
  onAcceptAISuggestion?: () => void;
  onRejectAISuggestion?: () => void;
  isVisible: boolean;
  animationDelay?: number;
}

export default function ProgressiveField({
  field,
  value,
  error,
  aiSuggestion,
  onChange,
  onAcceptAISuggestion,
  onRejectAISuggestion,
  isVisible,
  animationDelay = 0
}: ProgressiveFieldProps) {
  const [showHelp, setShowHelp] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  // Animación de entrada
  useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          delay: animationDelay,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          delay: animationDelay,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible, animationDelay]);

  const handleAcceptSuggestion = () => {
    if (onAcceptAISuggestion) {
      onAcceptAISuggestion();
    }
  };

  const handleRejectSuggestion = () => {
    if (onRejectAISuggestion) {
      onRejectAISuggestion();
    }
  };

  const showHelpDialog = () => {
    if (field.contextualHelp) {
      Alert.alert(
        field.label,
        field.contextualHelp,
        [{ text: 'Entendido', style: 'default' }]
      );
    }
  };

  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <TextInput
            style={[
              styles.textInput,
              isFocused && styles.textInputFocused,
              error && styles.textInputError,
            ]}
            value={value || ''}
            onChangeText={onChange}
            placeholder={field.placeholder}
            placeholderTextColor={Colors.light.textSecondary}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            multiline={field.id.includes('description') || field.id.includes('notes')}
            numberOfLines={field.id.includes('description') || field.id.includes('notes') ? 3 : 1}
          />
        );

      case 'select':
        return (
          <DiagnosisSelector
            label=""
            value={value || ''}
            options={field.validationRules?.find(r => r.type === 'options')?.value || []}
            onValueChange={onChange}
            required={field.required}
            isFromAI={!!aiSuggestion?.applied}
          />
        );

      case 'visual-select':
        return (
          <View style={styles.visualSelectContainer}>
            {/* Implementar selector visual personalizado */}
            <Text style={styles.placeholderText}>
              Selector visual (por implementar)
            </Text>
          </View>
        );

      case 'slider':
        return (
          <View style={styles.sliderContainer}>
            {/* Implementar slider personalizado */}
            <Text style={styles.placeholderText}>
              Slider (por implementar)
            </Text>
          </View>
        );

      case 'toggle':
        return (
          <TouchableOpacity
            style={[
              styles.toggleContainer,
              value && styles.toggleContainerActive
            ]}
            onPress={() => onChange(!value)}
          >
            <View style={[
              styles.toggleSwitch,
              value && styles.toggleSwitchActive
            ]}>
              <View style={[
                styles.toggleThumb,
                value && styles.toggleThumbActive
              ]} />
            </View>
            <Text style={[
              styles.toggleLabel,
              value && styles.toggleLabelActive
            ]}>
              {value ? 'Activado' : 'Desactivado'}
            </Text>
          </TouchableOpacity>
        );

      case 'color-picker':
        return (
          <View style={styles.colorPickerContainer}>
            {/* Implementar color picker personalizado */}
            <Text style={styles.placeholderText}>
              Selector de color (por implementar)
            </Text>
          </View>
        );

      case 'hair-map':
        return (
          <View style={styles.hairMapContainer}>
            {/* Implementar mapa de cabello interactivo */}
            <Text style={styles.placeholderText}>
              Mapa de cabello (por implementar)
            </Text>
          </View>
        );

      default:
        return (
          <TextInput
            style={[
              styles.textInput,
              isFocused && styles.textInputFocused,
              error && styles.textInputError,
            ]}
            value={value || ''}
            onChangeText={onChange}
            placeholder={field.placeholder}
            placeholderTextColor={Colors.light.textSecondary}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
          />
        );
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }
      ]}
    >
      {/* Header del campo */}
      <View style={styles.fieldHeader}>
        <View style={styles.labelContainer}>
          <Text style={[
            styles.label,
            field.required && styles.labelRequired
          ]}>
            {field.label}
            {field.required && <Text style={styles.requiredAsterisk}> *</Text>}
          </Text>
          
          {field.contextualHelp && (
            <TouchableOpacity
              onPress={showHelpDialog}
              style={styles.helpButton}
            >
              <HelpCircle size={16} color={Colors.light.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        {/* Indicador de nivel */}
        <View style={[
          styles.levelBadge,
          field.level === 1 && styles.levelBadgeEssential,
          field.level === 2 && styles.levelBadgeImportant,
          field.level === 3 && styles.levelBadgeAdvanced,
        ]}>
          <Text style={styles.levelBadgeText}>
            {field.level === 1 ? 'Esencial' : field.level === 2 ? 'Importante' : 'Avanzado'}
          </Text>
        </View>
      </View>

      {/* Descripción del campo */}
      {field.description && (
        <Text style={styles.description}>
          {field.description}
        </Text>
      )}

      {/* Sugerencia de IA */}
      {aiSuggestion && !aiSuggestion.applied && (
        <View style={styles.aiSuggestionContainer}>
          <View style={styles.aiSuggestionHeader}>
            <Zap size={16} color={Colors.light.primary} />
            <Text style={styles.aiSuggestionTitle}>
              Sugerencia IA ({aiSuggestion.confidence}% confianza)
            </Text>
          </View>
          
          <Text style={styles.aiSuggestionValue}>
            {String(aiSuggestion.value)}
          </Text>
          
          <View style={styles.aiSuggestionActions}>
            <BaseButton
              title="Aceptar"
              variant="outline"
              size="sm"
              icon={Check}
              onPress={handleAcceptSuggestion}
              style={styles.aiActionButton}
            />
            <BaseButton
              title="Rechazar"
              variant="ghost"
              size="sm"
              icon={X}
              onPress={handleRejectSuggestion}
              style={styles.aiActionButton}
            />
          </View>
        </View>
      )}

      {/* Campo de entrada */}
      <View style={styles.fieldContainer}>
        {renderField()}
        
        {/* Indicador de sugerencia aplicada */}
        {aiSuggestion?.applied && (
          <View style={styles.aiAppliedIndicator}>
            <Zap size={14} color={Colors.light.primary} />
          </View>
        )}
      </View>

      {/* Error de validación */}
      {error && (
        <View style={styles.errorContainer}>
          <AlertCircle size={16} color={Colors.light.error} />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {/* Indicador de validación exitosa */}
      {!error && value && field.required && (
        <View style={styles.successContainer}>
          <CheckCircle size={16} color={Colors.light.success} />
          <Text style={styles.successText}>Campo completado</Text>
        </View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.lg,
  },
  fieldHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  label: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  labelRequired: {
    color: Colors.light.text,
  },
  requiredAsterisk: {
    color: Colors.light.error,
  },
  helpButton: {
    marginLeft: spacing.xs,
    padding: spacing.xs,
  },
  levelBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.sm,
    backgroundColor: Colors.light.borderLight,
  },
  levelBadgeEssential: {
    backgroundColor: Colors.light.error + '20',
  },
  levelBadgeImportant: {
    backgroundColor: Colors.light.warning + '20',
  },
  levelBadgeAdvanced: {
    backgroundColor: Colors.light.info + '20',
  },
  levelBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
    color: Colors.light.textSecondary,
  },
  description: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.sm,
    lineHeight: 20,
  },
  aiSuggestionContainer: {
    backgroundColor: Colors.light.primaryLight + '10',
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderLeftWidth: 3,
    borderLeftColor: Colors.light.primary,
  },
  aiSuggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  aiSuggestionTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
    marginLeft: spacing.xs,
  },
  aiSuggestionValue: {
    fontSize: typography.sizes.md,
    color: Colors.light.text,
    marginBottom: spacing.sm,
    fontWeight: typography.weights.medium,
  },
  aiSuggestionActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  aiActionButton: {
    flex: 1,
  },
  fieldContainer: {
    position: 'relative',
  },
  textInput: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    padding: spacing.md,
    fontSize: typography.sizes.md,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
    minHeight: 48,
  },
  textInputFocused: {
    borderColor: Colors.light.primary,
    ...shadows.sm,
  },
  textInputError: {
    borderColor: Colors.light.error,
  },
  aiAppliedIndicator: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    backgroundColor: Colors.light.primary + '20',
    borderRadius: radius.full,
    padding: spacing.xs,
  },
  visualSelectContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: Colors.light.border,
    minHeight: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sliderContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: Colors.light.border,
    minHeight: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  toggleContainerActive: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primaryLight + '10',
  },
  toggleSwitch: {
    width: 50,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.light.borderLight,
    padding: 2,
    marginRight: spacing.md,
  },
  toggleSwitchActive: {
    backgroundColor: Colors.light.primary,
  },
  toggleThumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.textLight,
    ...shadows.sm,
  },
  toggleThumbActive: {
    transform: [{ translateX: 22 }],
  },
  toggleLabel: {
    fontSize: typography.sizes.md,
    color: Colors.light.textSecondary,
  },
  toggleLabelActive: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  colorPickerContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: Colors.light.border,
    minHeight: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  hairMapContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: Colors.light.border,
    minHeight: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: typography.sizes.md,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.sm,
    gap: spacing.xs,
  },
  errorText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.error,
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.sm,
    gap: spacing.xs,
  },
  successText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.success,
  },
});
