/**
 * AI Confidence Indicator Component
 * Indicador de confianza de las sugerencias de IA
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Modal,
  ScrollView
} from 'react-native';
import { 
  Zap, 
  Info, 
  CheckCircle, 
  AlertTriangle, 
  X,
  Brain,
  TrendingUp
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';
import { AIConfidenceScore } from '@/types/progressive-disclosure';
import BaseButton from '@/components/base/BaseButton';

interface AIConfidenceIndicatorProps {
  confidence: AIConfidenceScore;
  compact?: boolean;
  showDetails?: boolean;
}

export default function AIConfidenceIndicator({
  confidence,
  compact = false,
  showDetails = true
}: AIConfidenceIndicatorProps) {
  const [showModal, setShowModal] = useState(false);
  const [scaleAnim] = useState(new Animated.Value(1));

  const getConfidenceColor = (score: number) => {
    if (score >= 80) return Colors.light.success;
    if (score >= 60) return Colors.light.warning;
    return Colors.light.error;
  };

  const getConfidenceLabel = (score: number) => {
    if (score >= 90) return 'Muy Alta';
    if (score >= 80) return 'Alta';
    if (score >= 60) return 'Media';
    if (score >= 40) return 'Baja';
    return 'Muy Baja';
  };

  const getConfidenceIcon = (score: number) => {
    if (score >= 80) return CheckCircle;
    if (score >= 60) return AlertTriangle;
    return AlertTriangle;
  };

  const handlePress = () => {
    if (!showDetails) return;

    // Animación de tap
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    setShowModal(true);
  };

  const ConfidenceIcon = getConfidenceIcon(confidence.overall);
  const confidenceColor = getConfidenceColor(confidence.overall);

  if (compact) {
    return (
      <TouchableOpacity
        onPress={handlePress}
        disabled={!showDetails}
        style={styles.compactContainer}
      >
        <Animated.View
          style={[
            styles.compactContent,
            { transform: [{ scale: scaleAnim }] }
          ]}
        >
          <Zap size={16} color={confidenceColor} />
          <Text style={[styles.compactText, { color: confidenceColor }]}>
            {confidence.overall}%
          </Text>
        </Animated.View>
      </TouchableOpacity>
    );
  }

  return (
    <>
      <TouchableOpacity
        onPress={handlePress}
        disabled={!showDetails}
        style={styles.container}
      >
        <Animated.View
          style={[
            styles.content,
            { transform: [{ scale: scaleAnim }] }
          ]}
        >
          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <Brain size={20} color={Colors.light.primary} />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.title}>Análisis IA</Text>
              <Text style={styles.subtitle}>
                Confianza: {getConfidenceLabel(confidence.overall)}
              </Text>
            </View>
            <View style={styles.scoreContainer}>
              <ConfidenceIcon size={18} color={confidenceColor} />
              <Text style={[styles.score, { color: confidenceColor }]}>
                {confidence.overall}%
              </Text>
            </View>
          </View>

          {/* Barra de progreso */}
          <View style={styles.progressContainer}>
            <View style={styles.progressTrack} />
            <View
              style={[
                styles.progressFill,
                {
                  width: `${confidence.overall}%`,
                  backgroundColor: confidenceColor,
                }
              ]}
            />
          </View>

          {showDetails && (
            <View style={styles.detailsHint}>
              <Info size={14} color={Colors.light.textSecondary} />
              <Text style={styles.hintText}>Toca para ver detalles</Text>
            </View>
          )}
        </Animated.View>
      </TouchableOpacity>

      {/* Modal de detalles */}
      <Modal
        visible={showModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Análisis de Confianza IA</Text>
            <TouchableOpacity
              onPress={() => setShowModal(false)}
              style={styles.closeButton}
            >
              <X size={24} color={Colors.light.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Puntuación general */}
            <View style={styles.overallSection}>
              <View style={styles.overallHeader}>
                <Brain size={32} color={Colors.light.primary} />
                <View style={styles.overallText}>
                  <Text style={styles.overallScore}>
                    {confidence.overall}%
                  </Text>
                  <Text style={styles.overallLabel}>
                    Confianza General
                  </Text>
                </View>
              </View>
              
              <View style={styles.progressContainer}>
                <View style={styles.progressTrack} />
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${confidence.overall}%`,
                      backgroundColor: getConfidenceColor(confidence.overall),
                    }
                  ]}
                />
              </View>
            </View>

            {/* Puntuaciones por campo */}
            <View style={styles.fieldsSection}>
              <Text style={styles.sectionTitle}>Confianza por Campo</Text>
              {Object.entries(confidence.fieldScores).map(([fieldId, score]) => (
                <View key={fieldId} style={styles.fieldScore}>
                  <View style={styles.fieldHeader}>
                    <Text style={styles.fieldName}>
                      {fieldId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Text>
                    <Text style={[
                      styles.fieldScoreText,
                      { color: getConfidenceColor(score) }
                    ]}>
                      {score}%
                    </Text>
                  </View>
                  <View style={styles.fieldProgressContainer}>
                    <View style={styles.fieldProgressTrack} />
                    <View
                      style={[
                        styles.fieldProgressFill,
                        {
                          width: `${score}%`,
                          backgroundColor: getConfidenceColor(score),
                        }
                      ]}
                    />
                  </View>
                </View>
              ))}
            </View>

            {/* Razonamiento */}
            {confidence.reasoning.length > 0 && (
              <View style={styles.reasoningSection}>
                <Text style={styles.sectionTitle}>Razonamiento</Text>
                {confidence.reasoning.map((reason, index) => (
                  <View key={index} style={styles.reasonItem}>
                    <CheckCircle size={16} color={Colors.light.success} />
                    <Text style={styles.reasonText}>{reason}</Text>
                  </View>
                ))}
              </View>
            )}

            {/* Sugerencias */}
            {confidence.suggestions.length > 0 && (
              <View style={styles.suggestionsSection}>
                <Text style={styles.sectionTitle}>Sugerencias</Text>
                {confidence.suggestions.map((suggestion, index) => (
                  <View key={index} style={styles.suggestionItem}>
                    <TrendingUp size={16} color={Colors.light.primary} />
                    <Text style={styles.suggestionText}>{suggestion}</Text>
                  </View>
                ))}
              </View>
            )}
          </ScrollView>

          <View style={styles.modalFooter}>
            <BaseButton
              title="Cerrar"
              variant="primary"
              size="lg"
              onPress={() => setShowModal(false)}
              fullWidth
            />
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  compactContainer: {
    alignSelf: 'flex-end',
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    backgroundColor: Colors.light.surface,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.full,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  compactText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
  },
  container: {
    marginTop: spacing.md,
  },
  content: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: Colors.light.border,
    ...shadows.sm,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.primaryLight + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  subtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  score: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
  },
  progressContainer: {
    height: 6,
    backgroundColor: Colors.light.borderLight,
    borderRadius: radius.full,
    marginBottom: spacing.sm,
    overflow: 'hidden',
  },
  progressTrack: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.borderLight,
  },
  progressFill: {
    height: '100%',
    borderRadius: radius.full,
  },
  detailsHint: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    justifyContent: 'center',
  },
  hintText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  closeButton: {
    padding: spacing.sm,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  overallSection: {
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  overallHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  overallText: {
    marginLeft: spacing.md,
  },
  overallScore: {
    fontSize: typography.sizes['3xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  overallLabel: {
    fontSize: typography.sizes.md,
    color: Colors.light.textSecondary,
  },
  fieldsSection: {
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  fieldScore: {
    marginBottom: spacing.md,
  },
  fieldHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  fieldName: {
    fontSize: typography.sizes.md,
    color: Colors.light.text,
  },
  fieldScoreText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
  },
  fieldProgressContainer: {
    height: 4,
    backgroundColor: Colors.light.borderLight,
    borderRadius: radius.full,
    overflow: 'hidden',
  },
  fieldProgressTrack: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.borderLight,
  },
  fieldProgressFill: {
    height: '100%',
    borderRadius: radius.full,
  },
  reasoningSection: {
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  reasonItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.sm,
    marginBottom: spacing.sm,
  },
  reasonText: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: Colors.light.text,
    lineHeight: 22,
  },
  suggestionsSection: {
    paddingVertical: spacing.lg,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.sm,
    marginBottom: spacing.sm,
  },
  suggestionText: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: Colors.light.text,
    lineHeight: 22,
  },
  modalFooter: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
});
