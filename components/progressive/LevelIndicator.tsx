/**
 * Level Indicator Component
 * Indicador visual del progreso por niveles
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions
} from 'react-native';
import { CheckCircle, Circle, Clock } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, radius, typography } from '@/constants/theme';
import { DisclosureLevel } from '@/types/progressive-disclosure';

interface LevelIndicatorProps {
  currentLevel: number;
  completedLevels: number[];
  totalLevels: number;
  levelConfig?: DisclosureLevel;
  showTimeEstimate?: boolean;
  compact?: boolean;
}

export default function LevelIndicator({
  currentLevel,
  completedLevels,
  totalLevels,
  levelConfig,
  showTimeEstimate = true,
  compact = false
}: LevelIndicatorProps) {
  const progress = (completedLevels.length + (currentLevel > 0 ? 0.5 : 0)) / totalLevels;
  const progressWidth = `${Math.min(progress * 100, 100)}%`;

  const renderLevelDots = () => {
    const dots = [];
    
    for (let i = 1; i <= totalLevels; i++) {
      const isCompleted = completedLevels.includes(i);
      const isCurrent = i === currentLevel;
      const isPending = i > currentLevel && !isCompleted;

      dots.push(
        <View key={i} style={styles.dotContainer}>
          <View
            style={[
              styles.dot,
              isCompleted && styles.dotCompleted,
              isCurrent && styles.dotCurrent,
              isPending && styles.dotPending,
            ]}
          >
            {isCompleted ? (
              <CheckCircle size={16} color={Colors.light.textLight} />
            ) : (
              <Text
                style={[
                  styles.dotText,
                  isCurrent && styles.dotTextCurrent,
                  isPending && styles.dotTextPending,
                ]}
              >
                {i}
              </Text>
            )}
          </View>
          
          {!compact && (
            <Text
              style={[
                styles.dotLabel,
                isCurrent && styles.dotLabelCurrent,
              ]}
            >
              Nivel {i}
            </Text>
          )}
        </View>
      );

      // Línea conectora (excepto para el último dot)
      if (i < totalLevels) {
        dots.push(
          <View
            key={`line-${i}`}
            style={[
              styles.connectorLine,
              (isCompleted || (isCurrent && i < currentLevel)) && styles.connectorLineActive,
            ]}
          />
        );
      }
    }

    return dots;
  };

  if (compact) {
    return (
      <View style={styles.compactContainer}>
        <View style={styles.compactProgress}>
          <View style={styles.progressTrack} />
          <Animated.View
            style={[
              styles.progressFill,
              { width: progressWidth }
            ]}
          />
        </View>
        <Text style={styles.compactText}>
          {completedLevels.length} de {totalLevels} completados
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Barra de progreso principal */}
      <View style={styles.progressContainer}>
        <View style={styles.progressTrack} />
        <Animated.View
          style={[
            styles.progressFill,
            { width: progressWidth }
          ]}
        />
      </View>

      {/* Indicadores de nivel */}
      <View style={styles.levelsContainer}>
        {renderLevelDots()}
      </View>

      {/* Información del nivel actual */}
      {levelConfig && (
        <View style={styles.currentLevelInfo}>
          <Text style={styles.currentLevelTitle}>
            {levelConfig.name}
          </Text>
          
          {showTimeEstimate && levelConfig.estimatedTime > 0 && (
            <View style={styles.timeContainer}>
              <Clock size={14} color={Colors.light.textSecondary} />
              <Text style={styles.timeText}>
                ~{Math.ceil(levelConfig.estimatedTime / 60)} min restantes
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: spacing.md,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  compactProgress: {
    flex: 1,
    height: 6,
    position: 'relative',
  },
  compactText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  progressContainer: {
    height: 6,
    backgroundColor: Colors.light.borderLight,
    borderRadius: radius.full,
    marginBottom: spacing.lg,
    overflow: 'hidden',
  },
  progressTrack: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.borderLight,
    borderRadius: radius.full,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.light.primary,
    borderRadius: radius.full,
  },
  levelsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  dotContainer: {
    alignItems: 'center',
    flex: 1,
  },
  dot: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.borderLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  dotCompleted: {
    backgroundColor: Colors.light.success,
  },
  dotCurrent: {
    backgroundColor: Colors.light.primary,
    borderWidth: 3,
    borderColor: Colors.light.primaryLight,
  },
  dotPending: {
    backgroundColor: Colors.light.borderLight,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  dotText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold,
    color: Colors.light.textSecondary,
  },
  dotTextCurrent: {
    color: Colors.light.textLight,
  },
  dotTextPending: {
    color: Colors.light.textSecondary,
  },
  dotLabel: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  dotLabelCurrent: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  connectorLine: {
    height: 2,
    flex: 0.3,
    backgroundColor: Colors.light.borderLight,
    marginHorizontal: spacing.xs,
    marginBottom: 20, // Align with dots
  },
  connectorLineActive: {
    backgroundColor: Colors.light.primary,
  },
  currentLevelInfo: {
    backgroundColor: Colors.light.primaryLight + '20',
    borderRadius: radius.md,
    padding: spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
  },
  currentLevelTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  timeText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
});
