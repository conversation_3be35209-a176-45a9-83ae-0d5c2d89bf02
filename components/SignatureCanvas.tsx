import React, { useRef, forwardRef, useImperativeHandle, useState } from "react";
import { View, Platform, Dimensions } from "react-native";
import Svg, { Path } from "react-native-svg";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { useSharedValue, runOnJS } from "react-native-reanimated";

interface SignatureCanvasProps {
  onSignature?: (signature: string) => void;
  style?: any;
}

export interface SignatureCanvasRef {
  clear: () => void;
  getSignature: () => string | null;
}

const SignatureCanvas = forwardRef<SignatureCanvasRef, SignatureCanvasProps>(
  ({ onSignature, style }, ref) => {
    const [pathData, setPathData] = useState<string>("");
    const [hasSignature, setHasSignature] = useState(false);
    const currentPath = useSharedValue("");
    const isDrawing = useSharedValue(false);

    useImperativeHandle(ref, () => ({
      clear: () => {
        setPathData("");
        setHasSignature(false);
        currentPath.value = "";
        if (onSignature) {
          onSignature("");
        }
      },
      getSignature: () => {
        if (hasSignature && pathData) {
          const screenWidth = Dimensions.get('window').width;
          const canvasWidth = screenWidth - 80;
          const canvasHeight = 200;
          
          // Create a simple signature data string with dynamic viewBox
          const svgContent = `<svg viewBox="0 0 ${canvasWidth} ${canvasHeight}" xmlns="http://www.w3.org/2000/svg"><path d="${pathData}" stroke="black" stroke-width="3" fill="none"/></svg>`;
          
          // For web, use btoa if available, otherwise create a simple data string
          if (Platform.OS === 'web' && typeof btoa !== 'undefined') {
            return `data:image/svg+xml;base64,${btoa(svgContent)}`;
          } else {
            // For mobile or if btoa is not available, return the SVG content directly
            return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;
          }
        }
        return null;
      }
    }));

    const updatePath = (newPath: string) => {
      setPathData(newPath);
      setHasSignature(true);
      if (onSignature) {
        const screenWidth = Dimensions.get('window').width;
        const canvasWidth = screenWidth - 80;
        const canvasHeight = 200;
        
        const svgContent = `<svg viewBox="0 0 ${canvasWidth} ${canvasHeight}" xmlns="http://www.w3.org/2000/svg"><path d="${newPath}" stroke="black" stroke-width="3" fill="none"/></svg>`;
        
        if (Platform.OS === 'web' && typeof btoa !== 'undefined') {
          onSignature(`data:image/svg+xml;base64,${btoa(svgContent)}`);
        } else {
          onSignature(`data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`);
        }
      }
    };

    const panGesture = Gesture.Pan()
      .onStart((event) => {
        isDrawing.value = true;
        currentPath.value = `M${event.x},${event.y}`;
        runOnJS(updatePath)(currentPath.value);
      })
      .onUpdate((event) => {
        if (isDrawing.value) {
          currentPath.value += ` L${event.x},${event.y}`;
          runOnJS(updatePath)(currentPath.value);
        }
      })
      .onEnd(() => {
        isDrawing.value = false;
      });

    // Get the screen width and calculate canvas dimensions
    const screenWidth = Dimensions.get('window').width;
    const defaultWidth = screenWidth - 80; // Account for padding
    const defaultHeight = 200;
    
    const { width = defaultWidth, height = defaultHeight } = style || {};

    return (
      <View style={[{ backgroundColor: "#f9f9f9", width, height }, style]}>
        <GestureDetector gesture={panGesture}>
          <View style={{ flex: 1 }}>
            <Svg 
              width="100%" 
              height="100%" 
              style={{ position: 'absolute' }}
              viewBox={`0 0 ${width} ${height}`}
              preserveAspectRatio="xMidYMid meet"
            >
              {pathData ? (
                <Path
                  d={pathData}
                  stroke="black"
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  fill="none"
                />
              ) : null}
            </Svg>
          </View>
        </GestureDetector>
      </View>
    );
  }
);

SignatureCanvas.displayName = 'SignatureCanvas';

export default SignatureCanvas;