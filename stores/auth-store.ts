import { create } from "zustand";
import AsyncStorage from "@react-native-async-storage/async-storage";

interface User {
  email: string;
  name: string;
}

interface BrandLineSelection {
  brandId: string;
  selectedLines: string[]; // Array of line IDs
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  preferredBrandLines: BrandLineSelection[];
  setIsAuthenticated: (value: boolean) => void;
  login: (user: User) => Promise<void>;
  logout: () => Promise<void>;
  updatePreferredBrandLines: (brandLines: BrandLineSelection[]) => Promise<void>;
  addBrandLineSelection: (brandId: string, lineIds: string[]) => Promise<void>;
  removeBrandLineSelection: (brandId: string) => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  isAuthenticated: false,
  user: null,
  preferredBrandLines: [],
  setIsAuthenticated: (value) => set({ isAuthenticated: value }),
  login: async (user) => {
    try {
      await AsyncStorage.setItem("salonier-auth", JSON.stringify({ user }));
      set({ isAuthenticated: true, user });
    } catch (error) {
      console.error("Error storing auth data:", error);
      throw error;
    }
  },
  logout: async () => {
    try {
      await AsyncStorage.removeItem("salonier-auth");
      await AsyncStorage.removeItem("salonier-preferences");
      set({ isAuthenticated: false, user: null, preferredBrandLines: [] });
    } catch (error) {
      console.error("Error removing auth data:", error);
      throw error;
    }
  },
  updatePreferredBrandLines: async (brandLines) => {
    try {
      await AsyncStorage.setItem("salonier-preferences", JSON.stringify({ brandLines }));
      set({ preferredBrandLines: brandLines });
    } catch (error) {
      console.error("Error storing preferences:", error);
      throw error;
    }
  },
  addBrandLineSelection: async (brandId, lineIds) => {
    const { preferredBrandLines, updatePreferredBrandLines } = get();
    const existingIndex = preferredBrandLines.findIndex(bl => bl.brandId === brandId);
    
    let newBrandLines;
    if (existingIndex >= 0) {
      // Update existing brand selection
      newBrandLines = [...preferredBrandLines];
      newBrandLines[existingIndex] = { brandId, selectedLines: lineIds };
    } else {
      // Add new brand selection
      newBrandLines = [...preferredBrandLines, { brandId, selectedLines: lineIds }];
    }
    
    await updatePreferredBrandLines(newBrandLines);
  },
  removeBrandLineSelection: async (brandId) => {
    const { preferredBrandLines, updatePreferredBrandLines } = get();
    const newBrandLines = preferredBrandLines.filter(bl => bl.brandId !== brandId);
    await updatePreferredBrandLines(newBrandLines);
  },
}));