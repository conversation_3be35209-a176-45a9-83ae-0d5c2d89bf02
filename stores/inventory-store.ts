import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  Product, 
  StockMovement, 
  InventoryAlert, 
  ConsumptionAnalysis,
  InventoryReport 
} from '@/types/inventory';

interface InventoryStore {
  products: Product[];
  movements: StockMovement[];
  alerts: InventoryAlert[];
  lastSync: string | null;
  
  // Product Actions
  addProduct: (product: Omit<Product, 'id' | 'lastUpdated'>) => Promise<string>;
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  getProduct: (id: string) => Product | undefined;
  getProductByNameAndBrand: (name: string, brand: string) => Product | undefined;
  searchProducts: (query: string) => Product[];
  
  // Stock Actions
  updateStock: (productId: string, quantity: number, type: StockMovement['type'], reason: string, referenceId?: string) => Promise<void>;
  consumeProducts: (consumptions: Array<{ productId: string; quantity: number }>, referenceId: string, clientName: string) => Promise<void>;
  getStockMovements: (productId?: string, limit?: number) => StockMovement[];
  
  // Alert Actions
  createAlert: (productId: string, type: InventoryAlert['type'], message: string, severity: InventoryAlert['severity']) => void;
  acknowledgeAlert: (alertId: string, userId: string) => void;
  getActiveAlerts: () => InventoryAlert[];
  checkLowStock: () => void;
  
  // Analysis & Reports
  getConsumptionAnalysis: (productId: string, period: 'daily' | 'weekly' | 'monthly') => ConsumptionAnalysis | null;
  generateInventoryReport: () => InventoryReport;
  getProductsByCategory: (category: Product['category']) => Product[];
  getTotalInventoryValue: () => number;
  
  // Sync & Initialize
  initializeWithDefaults: () => void;
  generateMockMovements: () => void;
  clearAllData: () => void;
}

// Helper para generar IDs únicos
const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

// Productos predefinidos comunes
const defaultProducts: Omit<Product, 'id' | 'lastUpdated'>[] = [
  // Oxidantes
  {
    name: 'Oxidante 10 Vol (3%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 1000,
    minStock: 200,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  {
    name: 'Oxidante 20 Vol (6%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 1500,
    minStock: 300,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  {
    name: 'Oxidante 30 Vol (9%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 1000,
    minStock: 200,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  {
    name: 'Oxidante 40 Vol (12%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 500,
    minStock: 100,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  {
    name: 'Welloxon Perfect 20 Vol',
    brand: 'Wella',
    category: 'oxidante',
    currentStock: 2000,
    minStock: 400,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 8.5,
    costPerUnit: 0.0085,
    isActive: true,
  },
  // Tintes
  {
    name: 'Koleston Perfect 6/0',
    brand: 'Wella',
    category: 'tinte',
    currentStock: 5,
    minStock: 2,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 7.2,
    costPerUnit: 0.12,
    isActive: true,
  },
  {
    name: 'Koleston Perfect 7/0',
    brand: 'Wella',
    category: 'tinte',
    currentStock: 8,
    minStock: 3,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 7.2,
    costPerUnit: 0.12,
    isActive: true,
  },
  {
    name: 'Koleston Perfect 8/0',
    brand: 'Wella',
    category: 'tinte',
    currentStock: 12,
    minStock: 4,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 7.2,
    costPerUnit: 0.12,
    isActive: true,
  },
  {
    name: 'Majirel 6.3',
    brand: "L'Oréal",
    category: 'tinte',
    currentStock: 6,
    minStock: 2,
    unitType: 'ml',
    unitSize: 50,
    purchasePrice: 6.8,
    costPerUnit: 0.136,
    isActive: true,
  },
  {
    name: 'Majirel 7.31',
    brand: "L'Oréal",
    category: 'tinte',
    currentStock: 4,
    minStock: 2,
    unitType: 'ml',
    unitSize: 50,
    purchasePrice: 6.8,
    costPerUnit: 0.136,
    isActive: true,
  },
  {
    name: 'Igora Royal 5-0',
    brand: 'Schwarzkopf',
    category: 'tinte',
    currentStock: 3,
    minStock: 1,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 7.5,
    costPerUnit: 0.125,
    isActive: true,
  },
  {
    name: 'Igora Royal 9-00',
    brand: 'Schwarzkopf',
    category: 'tinte',
    currentStock: 7,
    minStock: 2,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 7.5,
    costPerUnit: 0.125,
    isActive: true,
  },
  // Decolorantes
  {
    name: 'Polvo Decolorante Azul',
    brand: 'Genérico',
    category: 'decolorante',
    currentStock: 500,
    minStock: 100,
    unitType: 'g',
    unitSize: 500,
    purchasePrice: 15,
    costPerUnit: 0.03,
    isActive: true,
  },
  {
    name: 'Blondor Multi Blonde',
    brand: 'Wella',
    category: 'decolorante',
    currentStock: 400,
    minStock: 100,
    unitType: 'g',
    unitSize: 800,
    purchasePrice: 28,
    costPerUnit: 0.035,
    isActive: true,
  },
  {
    name: 'Platinium Plus',
    brand: "L'Oréal",
    category: 'decolorante',
    currentStock: 300,
    minStock: 50,
    unitType: 'g',
    unitSize: 500,
    purchasePrice: 22,
    costPerUnit: 0.044,
    isActive: true,
  },
  // Tratamientos
  {
    name: 'Olaplex No.1',
    brand: 'Olaplex',
    category: 'tratamiento',
    currentStock: 300,
    minStock: 50,
    unitType: 'ml',
    unitSize: 100,
    purchasePrice: 80,
    costPerUnit: 0.8,
    isActive: true,
  },
  {
    name: 'Olaplex No.2',
    brand: 'Olaplex',
    category: 'tratamiento',
    currentStock: 500,
    minStock: 100,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 120,
    costPerUnit: 0.24,
    isActive: true,
  },
  {
    name: 'Smartbond Step 1',
    brand: "L'Oréal",
    category: 'tratamiento',
    currentStock: 250,
    minStock: 50,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 45,
    costPerUnit: 0.09,
    isActive: true,
  },
  {
    name: 'Fiberplex No.1',
    brand: 'Schwarzkopf',
    category: 'tratamiento',
    currentStock: 200,
    minStock: 40,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 55,
    costPerUnit: 0.11,
    isActive: true,
  },
  {
    name: 'Wellaplex No.1',
    brand: 'Wella',
    category: 'tratamiento',
    currentStock: 180,
    minStock: 30,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 50,
    costPerUnit: 0.10,
    isActive: true,
  },
  // Otros
  {
    name: 'Champú Silver',
    brand: "L'Oréal",
    category: 'otro',
    currentStock: 800,
    minStock: 200,
    unitType: 'ml',
    unitSize: 1500,
    purchasePrice: 18,
    costPerUnit: 0.012,
    isActive: true,
  },
  {
    name: 'Papel de Aluminio',
    brand: 'Genérico',
    category: 'otro',
    currentStock: 50,
    minStock: 10,
    maxStock: 100,
    unitType: 'unidad',
    unitSize: 1,
    purchasePrice: 12,
    costPerUnit: 12,
    isActive: true,
    notes: 'Rollos de 100 metros',
  },
];

export const useInventoryStore = create<InventoryStore>()(
  persist(
    (set, get) => ({
      products: [],
      movements: [],
      alerts: [],
      lastSync: null,

      addProduct: async (productData) => {
        const id = generateId();
        const newProduct: Product = {
          ...productData,
          id,
          lastUpdated: new Date().toISOString(),
        };
        
        set((state) => ({
          products: [...state.products, newProduct],
        }));
        
        // Check stock levels
        get().checkLowStock();
        
        return id;
      },

      updateProduct: async (id, updates) => {
        set((state) => ({
          products: state.products.map((p) =>
            p.id === id
              ? { ...p, ...updates, lastUpdated: new Date().toISOString() }
              : p
          ),
        }));
        
        // Check stock levels after update
        get().checkLowStock();
      },

      deleteProduct: async (id) => {
        set((state) => ({
          products: state.products.filter((p) => p.id !== id),
          movements: state.movements.filter((m) => m.productId !== id),
          alerts: state.alerts.filter((a) => a.productId !== id),
        }));
      },

      getProduct: (id) => {
        return get().products.find((p) => p.id === id);
      },

      getProductByNameAndBrand: (name, brand) => {
        const normalizedName = name.toLowerCase().trim();
        const normalizedBrand = brand.toLowerCase().trim();
        
        return get().products.find((p) => 
          p.name.toLowerCase().includes(normalizedName) && 
          p.brand.toLowerCase() === normalizedBrand
        );
      },

      searchProducts: (query) => {
        const normalizedQuery = query.toLowerCase().trim();
        return get().products.filter((p) =>
          p.name.toLowerCase().includes(normalizedQuery) ||
          p.brand.toLowerCase().includes(normalizedQuery) ||
          p.category.toLowerCase().includes(normalizedQuery) ||
          (p.barcode && p.barcode.includes(normalizedQuery))
        );
      },

      updateStock: async (productId, quantity, type, reason, referenceId) => {
        const product = get().getProduct(productId);
        if (!product) throw new Error('Producto no encontrado');
        
        const previousStock = product.currentStock;
        const newStock = type === 'salida' || type === 'consumo' 
          ? previousStock - Math.abs(quantity)
          : previousStock + Math.abs(quantity);
        
        // Update product stock
        await get().updateProduct(productId, { currentStock: newStock });
        
        // Create movement record
        const movement: StockMovement = {
          id: generateId(),
          productId,
          type,
          quantity: type === 'salida' || type === 'consumo' ? -Math.abs(quantity) : Math.abs(quantity),
          previousStock,
          newStock,
          reason,
          referenceId,
          date: new Date().toISOString(),
          createdBy: 'system', // TODO: Get from auth
          cost: Math.abs(quantity) * product.costPerUnit,
        };
        
        set((state) => ({
          movements: [movement, ...state.movements],
        }));
      },

      consumeProducts: async (consumptions, referenceId, clientName) => {
        for (const { productId, quantity } of consumptions) {
          const movement = get().movements.find(
            m => m.productId === productId && 
            m.referenceId === referenceId && 
            m.type === 'consumo'
          );
          
          if (!movement) {
            await get().updateStock(
              productId, 
              quantity, 
              'consumo', 
              `Consumo en servicio para ${clientName}`,
              referenceId
            );
          }
        }
      },

      getStockMovements: (productId, limit = 50) => {
        let movements = get().movements;
        
        if (productId) {
          movements = movements.filter((m) => m.productId === productId);
        }
        
        return movements.slice(0, limit);
      },

      createAlert: (productId, type, message, severity) => {
        const alert: InventoryAlert = {
          id: generateId(),
          productId,
          type,
          severity,
          message,
          acknowledged: false,
          createdAt: new Date().toISOString(),
        };
        
        set((state) => ({
          alerts: [alert, ...state.alerts],
        }));
      },

      acknowledgeAlert: (alertId, userId) => {
        set((state) => ({
          alerts: state.alerts.map((a) =>
            a.id === alertId
              ? { 
                  ...a, 
                  acknowledged: true, 
                  acknowledgedBy: userId,
                  acknowledgedAt: new Date().toISOString(),
                }
              : a
          ),
        }));
      },

      getActiveAlerts: () => {
        return get().alerts.filter((a) => !a.acknowledged);
      },

      checkLowStock: () => {
        const products = get().products;
        const existingAlerts = get().alerts;
        
        products.forEach((product) => {
          // Check if alert already exists
          const hasRecentAlert = existingAlerts.some(
            (a) => 
              a.productId === product.id && 
              a.type === 'low_stock' &&
              !a.acknowledged &&
              new Date(a.createdAt).getTime() > Date.now() - 24 * 60 * 60 * 1000 // 24 hours
          );
          
          if (!hasRecentAlert) {
            if (product.currentStock === 0) {
              get().createAlert(
                product.id,
                'out_of_stock',
                `${product.name} está agotado`,
                'high'
              );
            } else if (product.currentStock <= product.minStock) {
              get().createAlert(
                product.id,
                'low_stock',
                `${product.name} tiene stock bajo (${product.currentStock} ${product.unitType})`,
                'medium'
              );
            }
          }
        });
      },

      getConsumptionAnalysis: (productId, period) => {
        const movements = get().movements.filter(
          (m) => m.productId === productId && m.type === 'consumo'
        );
        
        if (movements.length === 0) return null;
        
        const product = get().getProduct(productId);
        if (!product) return null;
        
        // Calculate period range
        const now = new Date();
        let startDate = new Date();
        
        switch (period) {
          case 'daily':
            startDate.setDate(now.getDate() - 1);
            break;
          case 'weekly':
            startDate.setDate(now.getDate() - 7);
            break;
          case 'monthly':
            startDate.setMonth(now.getMonth() - 1);
            break;
        }
        
        const periodMovements = movements.filter(
          (m) => new Date(m.date) >= startDate
        );
        
        const totalConsumed = periodMovements.reduce(
          (sum, m) => sum + Math.abs(m.quantity),
          0
        );
        
        const totalCost = totalConsumed * product.costPerUnit;
        const servicesCount = new Set(periodMovements.map(m => m.referenceId)).size;
        
        // Group by client
        const clientConsumption = new Map<string, { amount: number; count: number }>();
        periodMovements.forEach((m) => {
          const clientName = m.clientName || 'Desconocido';
          const current = clientConsumption.get(clientName) || { amount: 0, count: 0 };
          clientConsumption.set(clientName, {
            amount: current.amount + Math.abs(m.quantity),
            count: current.count + 1,
          });
        });
        
        const topClients = Array.from(clientConsumption.entries())
          .map(([clientName, data]) => ({
            clientName,
            consumptionAmount: data.amount,
            serviceCount: data.count,
          }))
          .sort((a, b) => b.consumptionAmount - a.consumptionAmount)
          .slice(0, 5);
        
        return {
          productId,
          productName: product.name,
          totalConsumed,
          totalCost,
          averagePerService: servicesCount > 0 ? totalConsumed / servicesCount : 0,
          servicesCount,
          period,
          topClients,
        };
      },

      generateInventoryReport: () => {
        const products = get().products;
        const movements = get().movements;
        
        const totalValue = products.reduce(
          (sum, p) => sum + p.currentStock * p.costPerUnit,
          0
        );
        
        const lowStockCount = products.filter(
          (p) => p.currentStock <= p.minStock && p.currentStock > 0
        ).length;
        
        const outOfStockCount = products.filter((p) => p.currentStock === 0).length;
        
        const overstockCount = products.filter(
          (p) => p.maxStock && p.currentStock > p.maxStock
        ).length;
        
        // Most used products (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        const recentConsumptions = movements.filter(
          (m) => m.type === 'consumo' && new Date(m.date) >= thirtyDaysAgo
        );
        
        const usageMap = new Map<string, { count: number; amount: number }>();
        recentConsumptions.forEach((m) => {
          const current = usageMap.get(m.productId) || { count: 0, amount: 0 };
          usageMap.set(m.productId, {
            count: current.count + 1,
            amount: current.amount + Math.abs(m.quantity),
          });
        });
        
        const mostUsedProducts = Array.from(usageMap.entries())
          .map(([productId, usage]) => ({
            product: get().getProduct(productId)!,
            usageCount: usage.count,
            totalConsumed: usage.amount,
          }))
          .filter(item => item.product)
          .sort((a, b) => b.totalConsumed - a.totalConsumed)
          .slice(0, 10);
        
        // Least used products
        const lastUsageMap = new Map<string, Date>();
        movements
          .filter((m) => m.type === 'consumo')
          .forEach((m) => {
            const currentDate = lastUsageMap.get(m.productId);
            const movementDate = new Date(m.date);
            if (!currentDate || movementDate > currentDate) {
              lastUsageMap.set(m.productId, movementDate);
            }
          });
        
        const leastUsedProducts = products
          .map((p) => {
            const lastUse = lastUsageMap.get(p.id);
            const daysSinceLastUse = lastUse
              ? Math.floor((Date.now() - lastUse.getTime()) / (1000 * 60 * 60 * 24))
              : Infinity;
            return { product: p, daysSinceLastUse };
          })
          .filter((item) => item.daysSinceLastUse > 30)
          .sort((a, b) => b.daysSinceLastUse - a.daysSinceLastUse)
          .slice(0, 10);
        
        // Cost by category
        const categoryMap = new Map<string, number>();
        products.forEach((p) => {
          const currentCost = categoryMap.get(p.category) || 0;
          categoryMap.set(p.category, currentCost + p.currentStock * p.costPerUnit);
        });
        
        const costByCategory = Array.from(categoryMap.entries())
          .map(([category, totalCost]) => ({
            category,
            totalCost,
            percentage: (totalCost / totalValue) * 100,
          }))
          .sort((a, b) => b.totalCost - a.totalCost);
        
        return {
          totalValue,
          lowStockCount,
          outOfStockCount,
          overstockCount,
          mostUsedProducts,
          leastUsedProducts,
          costByCategory,
          generatedAt: new Date().toISOString(),
        };
      },

      getProductsByCategory: (category) => {
        return get().products.filter((p) => p.category === category);
      },

      getTotalInventoryValue: () => {
        return get().products.reduce(
          (sum, p) => sum + p.currentStock * p.costPerUnit,
          0
        );
      },

      initializeWithDefaults: () => {
        const existingProducts = get().products;
        
        if (existingProducts.length === 0) {
          const newProducts = defaultProducts.map((p) => ({
            ...p,
            id: generateId(),
            lastUpdated: new Date().toISOString(),
          }));
          
          set({
            products: newProducts,
            lastSync: new Date().toISOString(),
          });
          
          // Generate mock historical movements
          setTimeout(() => {
            get().generateMockMovements();
          }, 1000);
          
          // Check initial stock levels
          get().checkLowStock();
        }
      },
      
      generateMockMovements: () => {
        const products = get().products;
        const movements: StockMovement[] = [];
        
        // Generate random movements for the last 30 days
        products.forEach((product) => {
          // Skip if product is new
          if (product.category === 'otro') return;
          
          const movementCount = Math.floor(Math.random() * 10) + 5; // 5-15 movements per product
          
          for (let i = 0; i < movementCount; i++) {
            const daysAgo = Math.floor(Math.random() * 30);
            const date = new Date();
            date.setDate(date.getDate() - daysAgo);
            
            const isConsumption = Math.random() > 0.3; // 70% consumption, 30% other
            const quantity = isConsumption
              ? product.category === 'tinte' 
                ? Math.floor(Math.random() * 2) + 1 // 1-2 units for dyes
                : Math.floor(Math.random() * 50) + 10 // 10-60 units for others
              : Math.floor(Math.random() * 5) * product.unitSize; // Entries in full packages
            
            const movement: StockMovement = {
              id: generateId(),
              productId: product.id,
              type: isConsumption ? 'consumo' : Math.random() > 0.5 ? 'entrada' : 'ajuste',
              quantity: isConsumption ? -quantity : quantity,
              previousStock: 0, // Will be calculated
              newStock: 0, // Will be calculated
              reason: isConsumption 
                ? `Servicio de coloración - Cliente ${Math.floor(Math.random() * 100)}`
                : movement.type === 'entrada' 
                  ? 'Reposición de inventario'
                  : 'Ajuste de inventario',
              date: date.toISOString(),
              createdBy: 'system',
              cost: Math.abs(quantity) * product.costPerUnit,
              clientName: isConsumption ? `Cliente ${Math.floor(Math.random() * 100)}` : undefined,
              referenceId: isConsumption ? `service-${generateId()}` : undefined,
            };
            
            movements.push(movement);
          }
        });
        
        // Sort by date (oldest first)
        movements.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        
        set((state) => ({
          movements: [...movements, ...state.movements],
        }));
      },

      clearAllData: () => {
        set({
          products: [],
          movements: [],
          alerts: [],
          lastSync: null,
        });
      },
    }),
    {
      name: 'inventory-storage',
      storage: createJSONStorage(() => AsyncStorage),
      onRehydrateStorage: () => (state) => {
        // Check stock levels after rehydration
        if (state) {
          setTimeout(() => {
            state.checkLowStock();
          }, 1000);
        }
      },
    }
  )
);