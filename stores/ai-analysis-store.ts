import { create } from "zustand";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Result for a single zone
interface ZoneAnalysisResult {
  zone: string;
  depthLevel: number; // Now with decimals
  tone: string;
  undertone: string;
  state: string;
  grayPercentage?: number;
  porosity: string;
  elasticity: string;
  resistance: string;
  damage: string;
  unwantedTone?: string; // Detected unwanted tone
  confidence: number;
  // Nuevos campos profesionales
  grayType?: string; // Tipo de cana
  grayPattern?: string; // Patrón de distribución
  pigmentAccumulation?: string; // Acumulación de pigmentos
  cuticleState?: string; // Estado de la cutícula
  demarkationBands?: { location: number; contrast: string }[]; // Bandas de demarcación
}

// Complete AI analysis result
interface AIAnalysisResult {
  // General characteristics
  hairThickness: string;
  hairDensity: string;
  overallTone: string;
  overallUndertone: string;
  averageDepthLevel: number;
  
  // Zone-specific analysis
  zoneAnalysis: {
    roots: ZoneAnalysisResult;
    mids: ZoneAnalysisResult;
    ends: ZoneAnalysisResult;
  };
  
  // Chemical history detection
  detectedChemicalProcess?: string;
  estimatedLastProcessDate?: string;
  detectedHomeRemedies?: boolean;
  
  // Risk detection
  detectedRisks?: {
    metalSalts: { detected: boolean; confidence: number; signs: string[] };
    henna: { detected: boolean; confidence: number; signs: string[] };
    extremeDamage: { detected: boolean; zones: string[] };
  };
  
  // Service complexity
  serviceComplexity?: 'simple' | 'medium' | 'complex';
  estimatedTime?: number; // minutes
  
  // Overall assessment
  overallCondition: string;
  recommendations: string[];
  overallConfidence: number;
  analysisTimestamp: number;
}

// Photo analysis result for desired color photos
interface DesiredPhotoAnalysis {
  photoId: string;
  detectedLevel: number;
  detectedTone: string;
  detectedTechnique: string;
  detectedTones: string[];
  viabilityScore: number;
  estimatedSessions: number;
  requiredProcesses: string[];
  confidence: number;
  warnings?: string[];
}

interface AIAnalysisSettings {
  autoFaceBlur: boolean;
  imageQualityThreshold: number;
  privacyMode: boolean;
  saveAnalysisHistory: boolean;
  preferredAnalysisDepth: 'basic' | 'detailed' | 'expert';
}

interface AIAnalysisState {
  // Analysis state
  isAnalyzing: boolean;
  analysisResult: AIAnalysisResult | null;
  analysisHistory: AIAnalysisResult[];
  
  // Desired photo analysis
  isAnalyzingDesiredPhoto: boolean;
  desiredPhotoAnalyses: Record<string, DesiredPhotoAnalysis>;
  
  // Settings
  settings: AIAnalysisSettings;
  privacyMode: boolean;
  
  // Actions
  analyzeImage: (imageUri: string) => Promise<void>;
  analyzeDesiredPhoto: (photoId: string, imageUri: string, currentLevel: number) => Promise<DesiredPhotoAnalysis | null>;
  clearAnalysis: () => void;
  updateSettings: (settings: Partial<AIAnalysisSettings>) => Promise<void>;
  setPrivacyMode: (enabled: boolean) => void;
  getAnalysisHistory: () => AIAnalysisResult[];
  clearAnalysisHistory: () => Promise<void>;
}

// Mock AI analysis function - In production, this would call your Supabase Edge Function
const performAIAnalysis = async (imageUri: string): Promise<AIAnalysisResult> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Mock complete hair analysis with professional precision
  const mockResult: AIAnalysisResult = {
    // General characteristics
    hairThickness: "Medio",
    hairDensity: "Media",
    overallTone: "Castaño Medio",
    overallUndertone: "Dorado",
    averageDepthLevel: 6.7,
    
    // Zone-specific analysis with decimal precision
    zoneAnalysis: {
      roots: {
        zone: "Raíces",
        depthLevel: 6.2,
        tone: "Castaño Medio",
        undertone: "Natural",
        state: "Natural",
        grayPercentage: 25,
        grayType: "Resistente/Vidriosa",
        grayPattern: "Placas",
        porosity: "Media",
        elasticity: "Buena",
        resistance: "Media",
        damage: "Bajo",
        cuticleState: "Lisa",
        confidence: 92
      },
      mids: {
        zone: "Medios",
        depthLevel: 6.8,
        tone: "Castaño Claro",
        undertone: "Dorado",
        state: "Teñido",
        porosity: "Media",
        elasticity: "Media",
        resistance: "Media",
        damage: "Medio",
        unwantedTone: "Naranja", // Detected orange tones
        pigmentAccumulation: "Media",
        cuticleState: "Áspera",
        demarkationBands: [{ location: 5, contrast: "Medio" }],
        confidence: 88
      },
      ends: {
        zone: "Puntas",
        depthLevel: 7.3,
        tone: "Rubio Oscuro",
        undertone: "Cenizo",
        state: "Decolorado",
        porosity: "Alta",
        elasticity: "Pobre",
        resistance: "Baja",
        damage: "Alto",
        unwantedTone: "Amarillo", // Detected yellow tones
        pigmentAccumulation: "Alta",
        cuticleState: "Dañada",
        confidence: 95
      }
    },
    
    // Chemical history detection
    detectedChemicalProcess: "Decoloración y coloración",
    estimatedLastProcessDate: "Hace 2-3 meses",
    detectedHomeRemedies: false,
    
    // Risk detection
    detectedRisks: {
      metalSalts: {
        detected: false,
        confidence: 85,
        signs: []
      },
      henna: {
        detected: false,
        confidence: 90,
        signs: []
      },
      extremeDamage: {
        detected: true,
        zones: ["Puntas"]
      }
    },
    
    // Service complexity based on multiple factors
    serviceComplexity: 'medium' as const,
    estimatedTime: 120, // 2 hours
    
    // Overall assessment
    overallCondition: "Cabello con tratamiento químico previo, diferencia notable entre zonas",
    recommendations: [
      "⚠️ Canas vidrosas detectadas: aplicar pre-ablandamiento o doble pigmentación",
      "Banda de demarcación a 5cm: aplicar técnica de difuminado gradual",
      "Igualación de color recomendada antes de nuevo proceso",
      "Neutralización de tonos naranjas en medios con matiz azul-violeta",
      "Neutralización de tonos amarillos en puntas con matiz violeta",
      "Alta acumulación de pigmento en puntas: considerar limpieza suave",
      "Cutícula dañada en puntas: tratamiento de reconstrucción obligatorio",
      "Usar fórmula diferenciada por zonas para resultado uniforme",
      "Considerar pre-pigmentación en zonas más claras",
      "Tiempo de procesamiento: Raíces 35min, Medios 25min, Puntas 15min"
    ],
    overallConfidence: 91,
    analysisTimestamp: Date.now()
  };
  
  return mockResult;
};

// Mock function to analyze desired color photos
const analyzeDesiredColorPhoto = async (photoId: string, imageUri: string, currentLevel: number): Promise<DesiredPhotoAnalysis> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Mock analysis based on current level to simulate viability
  const targetLevel = 8.3; // Mock detected level
  const levelDifference = Math.abs(targetLevel - currentLevel);
  
  const mockAnalysis: DesiredPhotoAnalysis = {
    photoId,
    detectedLevel: targetLevel,
    detectedTone: "Rubio Claro",
    detectedTechnique: "Balayage con babylights",
    detectedTones: ["Beige", "Perla", "Champagne"],
    viabilityScore: Math.max(0, 100 - (levelDifference * 15)), // Lower score for bigger jumps
    estimatedSessions: levelDifference > 3 ? 2 : 1,
    requiredProcesses: levelDifference > 3 
      ? ["Decoloración progresiva", "Matización", "Tratamiento reconstructor"]
      : ["Decoloración controlada", "Matización"],
    confidence: 88,
    warnings: levelDifference > 4 
      ? ["Diferencia de más de 4 niveles detectada", "Se recomienda proceso en múltiples sesiones"]
      : undefined
  };
  
  return mockAnalysis;
};

export const useAIAnalysisStore = create<AIAnalysisState>((set, get) => ({
  // Initial state
  isAnalyzing: false,
  analysisResult: null,
  analysisHistory: [],
  isAnalyzingDesiredPhoto: false,
  desiredPhotoAnalyses: {},
  privacyMode: true,
  settings: {
    autoFaceBlur: true,
    imageQualityThreshold: 60,
    privacyMode: true,
    saveAnalysisHistory: false, // Default to false for privacy
    preferredAnalysisDepth: 'detailed'
  },

  // Actions
  analyzeImage: async (imageUri: string) => {
    set({ isAnalyzing: true, analysisResult: null });
    
    try {
      // In production, this would:
      // 1. Upload image to Supabase Storage (temporary bucket)
      // 2. Trigger Edge Function for analysis
      // 3. Edge Function would blur faces and analyze with OpenAI
      // 4. Return results and delete images
      
      const result = await performAIAnalysis(imageUri);
      console.log("AI Analysis Store - Result received:", result);
      
      set(state => ({
        isAnalyzing: false,
        analysisResult: result,
        analysisHistory: state.settings.saveAnalysisHistory 
          ? [...state.analysisHistory, result]
          : state.analysisHistory
      }));
      
      // Save to AsyncStorage if history is enabled
      const { settings, analysisHistory } = get();
      if (settings.saveAnalysisHistory) {
        try {
          await AsyncStorage.setItem(
            'salonier-analysis-history', 
            JSON.stringify(analysisHistory)
          );
        } catch (error) {
          console.error('Error saving analysis history:', error);
        }
      }
      
    } catch (error) {
      set({ isAnalyzing: false });
      throw error;
    }
  },

  analyzeDesiredPhoto: async (photoId: string, imageUri: string, currentLevel: number) => {
    set({ isAnalyzingDesiredPhoto: true });
    
    try {
      const analysis = await analyzeDesiredColorPhoto(photoId, imageUri, currentLevel);
      
      set(state => ({
        isAnalyzingDesiredPhoto: false,
        desiredPhotoAnalyses: {
          ...state.desiredPhotoAnalyses,
          [photoId]: analysis
        }
      }));
      
      return analysis;
    } catch (error) {
      set({ isAnalyzingDesiredPhoto: false });
      console.error('Error analyzing desired photo:', error);
      return null;
    }
  },

  clearAnalysis: () => {
    set({ analysisResult: null, desiredPhotoAnalyses: {} });
  },

  updateSettings: async (newSettings: Partial<AIAnalysisSettings>) => {
    const updatedSettings = { ...get().settings, ...newSettings };
    set({ settings: updatedSettings });
    
    try {
      await AsyncStorage.setItem(
        'salonier-ai-settings', 
        JSON.stringify(updatedSettings)
      );
    } catch (error) {
      console.error('Error saving AI settings:', error);
    }
  },

  setPrivacyMode: (enabled: boolean) => {
    set({ privacyMode: enabled });
  },

  getAnalysisHistory: () => {
    return get().analysisHistory;
  },

  clearAnalysisHistory: async () => {
    set({ analysisHistory: [] });
    try {
      await AsyncStorage.removeItem('salonier-analysis-history');
    } catch (error) {
      console.error('Error clearing analysis history:', error);
    }
  }
}));

// Initialize store with saved data
const initializeAIAnalysisStore = async () => {
  try {
    const savedSettings = await AsyncStorage.getItem('salonier-ai-settings');
    const savedHistory = await AsyncStorage.getItem('salonier-analysis-history');
    
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      useAIAnalysisStore.getState().updateSettings(settings);
    }
    
    if (savedHistory) {
      const history = JSON.parse(savedHistory);
      useAIAnalysisStore.setState({ analysisHistory: history });
    }
  } catch (error) {
    console.error('Error initializing AI analysis store:', error);
  }
};

// Call initialization
initializeAIAnalysisStore();