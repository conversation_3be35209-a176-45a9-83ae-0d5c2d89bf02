# Known Issues - <PERSON>ier Copilot

## Problemas Actuales

*No hay problemas conocidos en este momento. La aplicación está funcionando correctamente.*

---

## Problemas Resueltos

### ✅ RESUELTO: Error en Cálculo de Coste

**Fecha de reporte**: 2025-07-03  
**Fecha de resolución**: 2025-07-03  
**Severidad**: Media - No bloqueaba flujo pero afectaba funcionalidad  
**Estado**: RESUELTO

#### Descripción del Problema
Error "color.amount.replace is not a function" al calcular costes de formulación.

#### Causa Raíz
Inconsistencia entre el tipo `ColorFormula` (donde `amount` es número) y el código en `inventoryConsumptionService.ts` que esperaba un string.

#### Solución Implementada
1. Actualizado el parseo para manejar correctamente tipos numéricos
2. Corregido acceso a campos (`tone` en lugar de `shade`)
3. `brand` ahora se toma del objeto principal, no de cada color
4. Actualizado cálculo de oxidante para usar `developerVolume` y `developerRatio`

#### Verificación
Usuario confirmó que el cálculo de costes funciona correctamente.

### ⚠️ PARCIALMENTE RESUELTO: Crash de Cámara en "Color Deseado" (iOS)

**Fecha de reporte**: 2025-07-03  
**Última actualización**: 2025-07-05  
**Severidad**: CRÍTICA - Bloqueaba flujo principal  
**Estado**: PARCIALMENTE RESUELTO CON FALLBACK

#### Descripción del Problema
La aplicación se cierra (crash) cuando el usuario intenta usar la cámara guiada en la fase de "Resultado Deseado" del servicio en iOS. Este problema es específico de iOS y no ocurre en Android.

#### Causa Raíz
Bug conocido de expo-camera cuando se usa CameraView dentro de un Modal en iOS, especialmente en contextos con mucho estado acumulado (paso 1 del flujo tiene más complejidad que paso 0).

#### Intentos de Solución
1. **Delay de 100ms**: Funcionó temporalmente pero volvió a fallar
2. **Estados separados**: No resolvió el problema de fondo
3. **Navegación a pantalla dedicada**: Seguía crasheando
4. **Inline camera como diagnóstico**: Mejoró pero sigue inestable en iOS
5. **Reestructuración de GuidedCamera**: CameraView no soporta children, se usó posicionamiento absoluto
6. **Mover fuera del ScrollView**: Mejoró estabilidad pero no resolvió completamente

#### Solución Actual (Fallback)
- **iOS**: Usa automáticamente ImagePicker.launchCameraAsync() con mensaje explicativo al usuario
- **Android**: Continúa usando la cámara guiada sin problemas
- **UI**: Mantiene las 3 tarjetas de captura para consistencia visual

#### Archivos Modificados
- `app/service/new.tsx`: Implementación de fallback automático para iOS
- `components/GuidedCamera.tsx`: Reestructurado con posicionamiento absoluto
- `components/DesiredPhotoGallery.tsx`: Muestra 3 tarjetas desde el inicio

#### Estado Actual
- ✅ Funciona perfectamente en Android
- ⚠️ iOS usa cámara estándar sin guías visuales
- ✅ El flujo completo se puede completar en ambas plataformas
- 🔄 Esperando actualización de expo-camera para solución definitiva

---

## Problemas Menores

### Advertencias de Rutas No Existentes
**Severidad**: Baja  
**Descripción**: Warnings sobre rutas "auth" y "service/[id]" que no existen en el layout  
**Impacto**: Solo warnings, no afecta funcionalidad  
**Solución**: Actualizar _layout.tsx o crear las rutas faltantes