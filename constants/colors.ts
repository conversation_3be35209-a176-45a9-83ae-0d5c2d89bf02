// Premium color palette inspired by professional hair salon branding
const premiumBlack = "#1C1C1C";
const goldPrimary = "#D4A574";
const goldDark = "#B8941F";
const goldLight = "#E6C757";
const warmOrange = "#E8975B";
const beigeLight = "#F5E6D3";
const creamWhite = "#FFF8E7";
const successGreen = "#4CAF50";
const warmGray = "#8D7B68";

export default {
  light: {
    // Text colors
    text: "#2C2416",
    textSecondary: warmGray,
    textLight: "#FFFFFF",
    
    // Background colors
    background: creamWhite,
    backgroundSecondary: beigeLight,
    backgroundDark: premiumBlack,
    
    // Brand colors
    primary: goldPrimary,
    primaryDark: goldDark,
    primaryLight: goldLight,
    secondary: warmOrange,
    accent: "#CD853F",
    
    // UI elements
    card: beigeLight,
    cardDark: premiumBlack,
    surface: beigeLight,
    border: "#E0D5C7",
    borderLight: "#F0E8DC",
    
    // Navigation
    tabIconDefault: warmGray,
    tabIconSelected: goldPrimary,
    tint: goldPrimary,
    
    // Status colors
    success: successGreen,
    warning: "#F9A825",
    error: "#D32F2F",
    info: "#5C6BC0",
    
    // Grays
    gray: warmGray,
    grayLight: "#C4B5A0",
    lightGray: beigeLight,
    
    // Special UI states
    notification: warmOrange,
    highlight: goldLight,
    
    // AI Analysis specific colors
    aiProcessing: goldPrimary,
    aiSuccess: successGreen,
    aiWarning: "#F9A825",
    aiError: "#D32F2F",
    
    // Privacy and security colors
    privacy: successGreen,
    security: "#5C6BC0",
    
    // Quality indicators
    qualityExcellent: successGreen,
    qualityGood: goldPrimary,
    qualityPoor: "#D32F2F",
    
    // Progress indicators
    progressBackground: "#E0D5C7",
    progressFill: goldPrimary,
    
    // Shadows
    shadowColor: "#000000",
    shadowLight: "rgba(0, 0, 0, 0.05)",
    shadowMedium: "rgba(0, 0, 0, 0.1)",
  },
};