import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TouchableOpacity, TextInput, FlatList, Platform, Alert } from "react-native";
import { Link } from "expo-router";
import { Search, PlusCircle, Eye, Edit, Trash2, AlertCircle, Settings, Euro, Package } from "lucide-react-native";
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { BaseCard, BaseButton } from "@/components/base";
import { useInventoryStore } from "@/stores/inventory-store";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import PricingSetupModal from "@/components/inventory/PricingSetupModal";

export default function InventoryScreen() {
  const [searchQuery, setSearchQuery] = useState("");
  const [showPricingModal, setShowPricingModal] = useState(false);
  
  const { products, deleteProduct, initializeWithDefaults, searchProducts } = useInventoryStore();
  const { configuration, formatCurrency } = useSalonConfigStore();

  // Initialize default products if empty
  useEffect(() => {
    if (products.length === 0 && configuration.inventoryControlLevel !== 'solo-formulas') {
      setShowPricingModal(true);
    }
  }, [products.length, configuration.inventoryControlLevel]);

  const filteredInventory = searchQuery ? searchProducts(searchQuery) : products;

  const handleDeleteItem = (id: string, name: string) => {
    Alert.alert(
      'Eliminar Producto',
      `¿Estás seguro de que quieres eliminar "${name}"?\n\nEsta acción no se puede deshacer y eliminará también todo el historial de movimientos asociado.`,
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Eliminar', 
          style: 'destructive', 
          onPress: () => {
            deleteProduct(id);
            // Show success feedback
            Alert.alert('Éxito', 'Producto eliminado correctamente');
          }
        }
      ]
    );
  };

  const linkStyle = Platform.select({
    web: { textDecoration: 'none' },
    default: {}
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Search size={20} color={Colors.light.gray} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar por nombre, marca o categoría..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        <View style={styles.headerButtons}>
          <TouchableOpacity 
            style={styles.settingsButton}
            onPress={() => setShowPricingModal(true)}
          >
            <Settings size={20} color={Colors.light.primary} />
          </TouchableOpacity>
          <Link href="/inventory/new" style={linkStyle}>
            <View style={styles.addButton}>
              <PlusCircle size={20} color="white" />
              <Text style={styles.addButtonText}>Nuevo</Text>
            </View>
          </Link>
        </View>
      </View>

      <Text style={styles.description}>
        Gestiona tu inventario de productos. {configuration.inventoryControlLevel === 'smart-cost' && 'Los costos se calcularán automáticamente en cada servicio.'}
      </Text>

      {products.length === 0 ? (
        <View style={styles.emptyState}>
          <Package size={64} color={Colors.light.lightGray} />
          <Text style={styles.emptyStateTitle}>No hay productos en el inventario</Text>
          <Text style={styles.emptyStateText}>
            Comienza agregando productos o configura los precios iniciales
          </Text>
          <TouchableOpacity 
            style={styles.emptyStateButton}
            onPress={() => setShowPricingModal(true)}
          >
            <Text style={styles.emptyStateButtonText}>Configurar Precios</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <View style={styles.tableHeader}>
            <Text style={[styles.columnHeader, styles.nameColumn]}>Producto</Text>
            <Text style={[styles.columnHeader, styles.brandColumn]}>Marca</Text>
            <Text style={[styles.columnHeader, styles.quantityColumn]}>Stock</Text>
            <Text style={[styles.columnHeader, styles.priceColumn]}>€/unidad</Text>
            <Text style={[styles.columnHeader, styles.actionsColumn]}>Acciones</Text>
          </View>

          <FlatList
            data={filteredInventory}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View style={styles.tableRow}>
                <View style={[styles.nameColumn, styles.productCell]}>
                  <Text style={styles.productName} numberOfLines={1}>
                    {item.name}
                  </Text>
                  <Text style={styles.productType} numberOfLines={1}>
                    {item.category}
                  </Text>
                </View>
                <Text style={[styles.cellText, styles.brandColumn]} numberOfLines={1}>
                  {item.brand}
                </Text>
                <View style={[styles.quantityColumn, styles.quantityCell]}>
                  <Text style={[
                    styles.quantityText,
                    item.currentStock <= item.minStock && styles.lowStockText
                  ]}>
                    {item.currentStock} {item.unitType}
                  </Text>
                  {item.currentStock <= item.minStock && (
                    <AlertCircle size={14} color={Colors.light.warning} style={styles.alertIcon} />
                  )}
                </View>
                <Text style={[styles.cellText, styles.priceColumn]}>
                  {formatCurrency(item.costPerUnit)}/{item.unitType}
                </Text>
                <View style={[styles.actionsColumn, styles.actionsContainer]}>
                  <Link href={`/inventory/${item.id}`} style={linkStyle}>
                    <View style={styles.actionButton}>
                      <Eye size={16} color={Colors.light.primary} />
                    </View>
                  </Link>
                  <Link href={`/inventory/edit/${item.id}`} style={linkStyle}>
                    <View style={styles.actionButton}>
                      <Edit size={16} color={Colors.light.primary} />
                    </View>
                  </Link>
                  <TouchableOpacity style={styles.actionButton} onPress={() => handleDeleteItem(item.id, item.name)}>
                    <Trash2 size={16} color={Colors.light.error} />
                  </TouchableOpacity>
                </View>
              </View>
            )}
            ListEmptyComponent={
              <View style={styles.emptySearchState}>
                <Text style={styles.emptySearchText}>No se encontraron productos</Text>
              </View>
            }
          />
        </>
      )}

      <PricingSetupModal
        visible={showPricingModal}
        onClose={() => setShowPricingModal(false)}
        onComplete={() => {
          setShowPricingModal(false);
          // Reload products will happen automatically via zustand subscription
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
    padding: spacing.md,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  searchContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    marginRight: spacing.sm,
    height: 48,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  searchIcon: {
    marginRight: spacing.sm,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...shadows.sm,
  },
  addButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
    marginLeft: spacing.xs,
  },
  description: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.md,
  },
  tableHeader: {
    flexDirection: "row",
    backgroundColor: Colors.light.card,
    borderTopLeftRadius: radius.md,
    borderTopRightRadius: radius.md,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  columnHeader: {
    fontWeight: typography.weights.bold,
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  nameColumn: {
    flex: 2.5,
  },
  brandColumn: {
    flex: 1.5,
  },
  quantityColumn: {
    flex: 1.2,
  },
  priceColumn: {
    flex: 1.3,
    alignItems: "center",
  },
  actionsColumn: {
    flex: 1.5,
  },
  tableRow: {
    flexDirection: "row",
    backgroundColor: Colors.light.card,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  cellText: {
    fontSize: 14,
  },
  productCell: {
    justifyContent: "center",
  },
  productName: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
  },
  productType: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  quantityCell: {
    flexDirection: "row",
    alignItems: "center",
  },
  quantityText: {
    fontSize: 14,
  },
  lowStockText: {
    color: Colors.light.warning,
  },
  alertIcon: {
    marginLeft: 5,
  },
  actionsContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  actionButton: {
    marginLeft: 10,
    padding: 4,
  },
  emptyState: {
    alignItems: "center",
    backgroundColor: Colors.light.card,
    padding: spacing.xl,
    borderRadius: radius.lg,
    ...shadows.sm,
    marginTop: spacing.lg,
  },
  emptyStateText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.md,
  },
  emptyStateButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
    borderRadius: radius.full,
    ...shadows.sm,
  },
  emptyStateButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
  },
  headerButtons: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.sm,
  },
  settingsButton: {
    padding: spacing.sm,
    borderRadius: radius.sm,
    backgroundColor: Colors.light.surface,
  },
  emptyStateTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptySearchState: {
    alignItems: "center",
    padding: 40,
  },
  emptySearchText: {
    fontSize: 16,
    color: Colors.light.gray,
  },
});