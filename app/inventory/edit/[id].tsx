import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Switch,
} from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { ChevronLeft, ChevronDown } from "lucide-react-native";
import Colors from "@/constants/colors";
import { useInventoryStore } from "@/stores/inventory-store";
import { Product } from "@/types/inventory";

export default function EditInventoryItemScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { getProduct, updateProduct, searchProducts } = useInventoryStore();

  const [brand, setBrand] = useState("");
  const [productType, setProductType] = useState("");
  const [line, setLine] = useState("");
  const [productName, setProductName] = useState("");
  const [unit, setUnit] = useState<"ml" | "g" | "unidad">("ml");
  const [size, setSize] = useState("");
  const [lowStockThreshold, setLowStockThreshold] = useState("");
  const [barcode, setBarcode] = useState("");
  const [purchasePrice, setPurchasePrice] = useState("");
  const [category, setCategory] = useState<Product["category"]>("otro");
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [notes, setNotes] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [originalProduct, setOriginalProduct] = useState<Product | null>(null);

  const categories: Array<{ value: Product["category"]; label: string }> = [
    { value: "tinte", label: "Tinte" },
    { value: "oxidante", label: "Oxidante" },
    { value: "decolorante", label: "Decolorante" },
    { value: "tratamiento", label: "Tratamiento" },
    { value: "otro", label: "Otro" },
  ];

  useEffect(() => {
    if (id) {
      const product = getProduct(id);
      if (product) {
        setOriginalProduct(product);
        
        // Parse product name to extract components
        const nameParts = product.name.split(" ");
        setBrand(product.brand);
        setProductType(nameParts[0] || "");
        setLine(nameParts.length > 2 ? nameParts.slice(1, -1).join(" ") : "");
        setProductName(nameParts[nameParts.length - 1] || "");
        
        setCategory(product.category);
        setUnit(product.unitType);
        setSize(product.unitSize.toString());
        setLowStockThreshold(product.minStock.toString());
        setPurchasePrice(product.purchasePrice.toString());
        setBarcode(product.barcode || "");
        setNotes(product.notes || "");
        setIsActive(product.isActive);
      } else {
        Alert.alert("Error", "Producto no encontrado");
        router.back();
      }
    }
  }, [id]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!brand.trim()) {
      newErrors.brand = "La marca es obligatoria";
    }

    if (!productType.trim()) {
      newErrors.productType = "El tipo de producto es obligatorio";
    }

    if (!size.trim() || parseFloat(size) <= 0) {
      newErrors.size = "El tamaño del envase es obligatorio";
    }

    if (!purchasePrice.trim() || parseFloat(purchasePrice) <= 0) {
      newErrors.purchasePrice = "El precio de compra es obligatorio";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm() || !originalProduct) {
      return;
    }

    setIsLoading(true);

    try {
      const productDisplayName = [productType, line, productName]
        .filter(Boolean)
        .join(" ")
        .trim();

      const costPerUnit = parseFloat(purchasePrice) / parseFloat(size);

      // Check for duplicates (excluding current product)
      const existingProducts = searchProducts(
        `${brand} ${productType} ${productName}`.trim()
      );
      const duplicate = existingProducts.find(
        (p) =>
          p.id !== originalProduct.id &&
          p.brand.toLowerCase() === brand.toLowerCase() &&
          p.name.toLowerCase() === productDisplayName.toLowerCase()
      );

      if (duplicate) {
        Alert.alert(
          "Producto Duplicado",
          `Ya existe un producto similar: ${duplicate.brand} ${duplicate.name}. ¿Deseas continuar?`,
          [
            { text: "Cancelar", style: "cancel" },
            { text: "Continuar", onPress: () => saveProduct() },
          ]
        );
        return;
      }

      await saveProduct();
    } catch (error) {
      console.error("Error saving inventory item:", error);
      Alert.alert("Error", "No se pudo guardar el producto");
    } finally {
      setIsLoading(false);
    }
  };

  const saveProduct = async () => {
    if (!originalProduct) return;

    const productDisplayName = [productType, line, productName]
      .filter(Boolean)
      .join(" ")
      .trim();

    const costPerUnit = parseFloat(purchasePrice) / parseFloat(size);

    await updateProduct(originalProduct.id, {
      name: productDisplayName || productType,
      brand: brand.trim(),
      category,
      minStock: parseFloat(lowStockThreshold) || 0,
      unitType: unit,
      unitSize: parseFloat(size),
      purchasePrice: parseFloat(purchasePrice),
      costPerUnit,
      isActive,
      barcode: barcode.trim() || undefined,
      notes: notes.trim() || undefined,
    });

    Alert.alert("Éxito", "Producto actualizado correctamente", [
      { text: "OK", onPress: () => router.back() },
    ]);
  };

  if (!originalProduct) {
    return null;
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={100}
    >
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ChevronLeft size={24} color={Colors.light.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Editar Producto</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Información del Artículo</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Marca *</Text>
            <TextInput
              style={[styles.input, errors.brand && styles.inputError]}
              value={brand}
              onChangeText={setBrand}
              placeholder="Ej: Wella Professionals"
            />
            {errors.brand && <Text style={styles.errorText}>{errors.brand}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Categoría *</Text>
            <TouchableOpacity
              style={[styles.input, styles.pickerInput]}
              onPress={() => setShowCategoryPicker(!showCategoryPicker)}
            >
              <Text style={styles.pickerText}>
                {categories.find((c) => c.value === category)?.label ||
                  "Seleccionar"}
              </Text>
              <ChevronDown size={20} color={Colors.light.text} />
            </TouchableOpacity>
            {showCategoryPicker && (
              <View style={styles.pickerOptions}>
                {categories.map((cat) => (
                  <TouchableOpacity
                    key={cat.value}
                    style={styles.pickerOption}
                    onPress={() => {
                      setCategory(cat.value);
                      setShowCategoryPicker(false);
                    }}
                  >
                    <Text
                      style={[
                        styles.pickerOptionText,
                        category === cat.value && styles.pickerOptionTextSelected,
                      ]}
                    >
                      {cat.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Tipo de Producto *</Text>
            <TextInput
              style={[styles.input, errors.productType && styles.inputError]}
              value={productType}
              onChangeText={setProductType}
              placeholder="Ej: Tinte, Oxidante, Champú"
            />
            {errors.productType && (
              <Text style={styles.errorText}>{errors.productType}</Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Línea (Opcional)</Text>
            <TextInput
              style={styles.input}
              value={line}
              onChangeText={setLine}
              placeholder="Ej: Illumina Color, Koleston Perfect"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Nombre Producto (con Tono/Ref)</Text>
            <TextInput
              style={styles.input}
              value={productName}
              onChangeText={setProductName}
              placeholder="Ej: Koleston 8/0 Rubio Claro"
            />
          </View>

          <View style={styles.rowContainer}>
            <View style={[styles.formGroup, styles.rowItem]}>
              <Text style={styles.label}>Tamaño del Envase *</Text>
              <TextInput
                style={[styles.input, errors.size && styles.inputError]}
                value={size}
                onChangeText={setSize}
                placeholder="Ej: 60"
                keyboardType="numeric"
              />
              {errors.size && <Text style={styles.errorText}>{errors.size}</Text>}
            </View>

            <View style={[styles.formGroup, styles.rowItem]}>
              <Text style={styles.label}>Umbral Bajo Stock</Text>
              <TextInput
                style={styles.input}
                value={lowStockThreshold}
                onChangeText={setLowStockThreshold}
                placeholder="Ej: 2"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Precio de Compra *</Text>
            <TextInput
              style={[styles.input, errors.purchasePrice && styles.inputError]}
              value={purchasePrice}
              onChangeText={setPurchasePrice}
              placeholder="Ej: 25.50"
              keyboardType="decimal-pad"
            />
            {errors.purchasePrice && (
              <Text style={styles.errorText}>{errors.purchasePrice}</Text>
            )}
            {purchasePrice && size && (
              <Text style={styles.helperText}>
                Costo por {unit}: $
                {(parseFloat(purchasePrice) / parseFloat(size)).toFixed(3)}
              </Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Código Barras EAN (Opcional)</Text>
            <TextInput
              style={styles.input}
              value={barcode}
              onChangeText={setBarcode}
              placeholder="Ej: 8005610433172"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Notas (Opcional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Añade notas o información adicional"
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.switchGroup}>
            <View>
              <Text style={styles.label}>Producto Activo</Text>
              <Text style={styles.switchHelperText}>
                Los productos inactivos no aparecerán en las búsquedas
              </Text>
            </View>
            <Switch
              value={isActive}
              onValueChange={setIsActive}
              trackColor={{
                false: Colors.light.lightGray,
                true: Colors.light.primary,
              }}
            />
          </View>

          <View style={styles.stockInfo}>
            <Text style={styles.stockInfoTitle}>Stock Actual</Text>
            <Text style={styles.stockInfoValue}>
              {originalProduct.currentStock} {originalProduct.unitType}
            </Text>
            <Text style={styles.stockInfoNote}>
              Para modificar el stock, usa los botones de entrada/salida en el
              detalle del producto
            </Text>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => router.back()}
              disabled={isLoading}
            >
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={isLoading}
            >
              <Text style={styles.saveButtonText}>
                {isLoading ? "Guardando..." : "Guardar Cambios"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F5F7",
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  placeholder: {
    width: 34,
  },
  formContainer: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 15,
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 15,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
  },
  inputError: {
    borderColor: Colors.light.error,
  },
  errorText: {
    color: Colors.light.error,
    fontSize: 12,
    marginTop: 5,
  },
  rowContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  rowItem: {
    flex: 1,
    marginRight: 10,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: "center",
    marginRight: 10,
  },
  cancelButtonText: {
    color: Colors.light.text,
    fontWeight: "600",
  },
  saveButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: "center",
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: "white",
    fontWeight: "600",
  },
  pickerInput: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  pickerText: {
    fontSize: 15,
    color: Colors.light.text,
  },
  pickerOptions: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    marginTop: 5,
    overflow: "hidden",
  },
  pickerOption: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  pickerOptionText: {
    fontSize: 15,
    color: Colors.light.text,
  },
  pickerOptionTextSelected: {
    color: Colors.light.primary,
    fontWeight: "600",
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: 5,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  switchGroup: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
    paddingVertical: 10,
  },
  switchHelperText: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 4,
  },
  stockInfo: {
    backgroundColor: Colors.light.background,
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  stockInfoTitle: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 4,
  },
  stockInfoValue: {
    fontSize: 20,
    fontWeight: "600",
    color: Colors.light.primary,
    marginBottom: 8,
  },
  stockInfoNote: {
    fontSize: 12,
    color: Colors.light.gray,
  },
});