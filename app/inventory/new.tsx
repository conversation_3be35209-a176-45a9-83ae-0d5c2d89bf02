import React, { useState } from "react";
import { StyleSheet, Text, View, TextInput, TouchableOpacity, ScrollView, KeyboardAvoidingView, Platform, Alert } from "react-native";
import { router } from "expo-router";
import { ChevronLeft, Sparkles, ChevronDown } from "lucide-react-native";
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { BaseCard, BaseButton } from "@/components/base";
import { useInventoryStore } from "@/stores/inventory-store";
import { Product } from "@/types/inventory";

export default function NewInventoryItemScreen() {
  const { addProduct, searchProducts } = useInventoryStore();
  
  const [brand, setBrand] = useState("");
  const [productType, setProductType] = useState("");
  const [line, setLine] = useState("");
  const [productName, setProductName] = useState("");
  const [quantity, setQuantity] = useState("");
  const [unit, setUnit] = useState("ml");
  const [size, setSize] = useState("");
  const [lowStockThreshold, setLowStockThreshold] = useState("");
  const [barcode, setBarcode] = useState("");
  const [purchasePrice, setPurchasePrice] = useState("");
  const [category, setCategory] = useState<Product['category']>('otro');
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [aiInput, setAiInput] = useState("");
  
  const categories: Array<{ value: Product['category']; label: string }> = [
    { value: 'tinte', label: 'Tinte' },
    { value: 'oxidante', label: 'Oxidante' },
    { value: 'decolorante', label: 'Decolorante' },
    { value: 'tratamiento', label: 'Tratamiento' },
    { value: 'otro', label: 'Otro' },
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!brand.trim()) {
      newErrors.brand = "La marca es obligatoria";
    }
    
    if (!productType.trim()) {
      newErrors.productType = "El tipo de producto es obligatorio";
    }
    
    if (!size.trim() || parseFloat(size) <= 0) {
      newErrors.size = "El tamaño del envase es obligatorio";
    }
    
    if (!purchasePrice.trim() || parseFloat(purchasePrice) <= 0) {
      newErrors.purchasePrice = "El precio de compra es obligatorio";
    }
    
    if (!quantity.trim() || parseFloat(quantity) < 0) {
      newErrors.quantity = "La cantidad inicial es obligatoria";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Check for duplicates
      const existingProducts = searchProducts(`${brand} ${productType} ${productName}`.trim());
      if (existingProducts.length > 0) {
        const duplicate = existingProducts.find(p => 
          p.brand.toLowerCase() === brand.toLowerCase() &&
          p.name.toLowerCase().includes(productType.toLowerCase())
        );
        
        if (duplicate) {
          Alert.alert(
            "Producto Duplicado",
            `Ya existe un producto similar: ${duplicate.brand} ${duplicate.name}. ¿Deseas continuar?`,
            [
              { text: "Cancelar", style: "cancel" },
              { text: "Continuar", onPress: () => saveProduct() }
            ]
          );
          return;
        }
      }
      
      await saveProduct();
    } catch (error) {
      console.error("Error saving inventory item:", error);
      Alert.alert("Error", "No se pudo guardar el producto");
    } finally {
      setIsLoading(false);
    }
  };
  
  const saveProduct = async () => {
    const productDisplayName = [
      productType,
      line,
      productName
    ].filter(Boolean).join(' ').trim();
    
    const costPerUnit = parseFloat(purchasePrice) / parseFloat(size);
    
    await addProduct({
      name: productDisplayName || productType,
      brand: brand.trim(),
      category,
      currentStock: parseFloat(quantity) || 0,
      minStock: parseFloat(lowStockThreshold) || 0,
      unitType: unit as 'ml' | 'g' | 'unidad',
      unitSize: parseFloat(size),
      purchasePrice: parseFloat(purchasePrice),
      costPerUnit,
      isActive: true,
      barcode: barcode.trim() || undefined,
    });
    
    router.push("/inventory");
  };
  
  const analyzeAIInput = () => {
    if (!aiInput.trim()) return;
    
    // Simple parsing logic - can be enhanced
    const input = aiInput.toLowerCase();
    
    // Try to extract brand (common brands)
    const brands = ['wella', 'loreal', 'schwarzkopf', 'revlon', 'koleston', 'igora', 'majirel'];
    const foundBrand = brands.find(b => input.includes(b));
    if (foundBrand) {
      setBrand(foundBrand.charAt(0).toUpperCase() + foundBrand.slice(1));
    }
    
    // Try to extract category
    if (input.includes('tinte') || input.includes('color')) {
      setCategory('tinte');
      setProductType('Tinte');
    } else if (input.includes('oxidante') || input.includes('peróxido')) {
      setCategory('oxidante');
      setProductType('Oxidante');
    } else if (input.includes('decolorante') || input.includes('polvo')) {
      setCategory('decolorante');
      setProductType('Decolorante');
    } else if (input.includes('tratamiento') || input.includes('olaplex')) {
      setCategory('tratamiento');
      setProductType('Tratamiento');
    }
    
    // Try to extract size
    const sizeMatch = input.match(/(\d+)\s*(ml|g)/i);
    if (sizeMatch) {
      setSize(sizeMatch[1]);
      setUnit(sizeMatch[2].toLowerCase());
    }
    
    // Try to extract tone/reference
    const toneMatch = input.match(/(\d+[/\\]\d+)|([\d.]+)/g);
    if (toneMatch && toneMatch.length > 0) {
      setProductName(toneMatch[0]);
    }
    
    Alert.alert("Análisis Completado", "Revisa y completa los campos detectados");
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={100}
    >
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color={Colors.light.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Añadir Nuevo Artículo</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.formContainer}>
          <View style={styles.aiSection}>
            <View style={styles.aiHeader}>
              <Sparkles size={20} color={Colors.light.primary} />
              <Text style={styles.aiTitle}>Entrada Rápida con IA (Opcional)</Text>
            </View>
            <TextInput
              style={[styles.input, styles.aiInput]}
              placeholder="Describe el producto (ej: 'Tinte Wella Koleston 8/0 60ml')"
              value={aiInput}
              onChangeText={setAiInput}
            />
            <TouchableOpacity style={styles.aiButton} onPress={analyzeAIInput}>
              <Text style={styles.aiButtonText}>Analizar</Text>
            </TouchableOpacity>
          </View>

          <Text style={styles.sectionTitle}>Información del Artículo</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Marca *</Text>
            <TextInput
              style={[styles.input, errors.brand && styles.inputError]}
              value={brand}
              onChangeText={setBrand}
              placeholder="Ej: Wella Professionals"
            />
            {errors.brand && <Text style={styles.errorText}>{errors.brand}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Categoría *</Text>
            <TouchableOpacity 
              style={[styles.input, styles.pickerInput]}
              onPress={() => setShowCategoryPicker(!showCategoryPicker)}
            >
              <Text style={styles.pickerText}>
                {categories.find(c => c.value === category)?.label || 'Seleccionar'}
              </Text>
              <ChevronDown size={20} color={Colors.light.text} />
            </TouchableOpacity>
            {showCategoryPicker && (
              <View style={styles.pickerOptions}>
                {categories.map((cat) => (
                  <TouchableOpacity
                    key={cat.value}
                    style={styles.pickerOption}
                    onPress={() => {
                      setCategory(cat.value);
                      setShowCategoryPicker(false);
                    }}
                  >
                    <Text style={[
                      styles.pickerOptionText,
                      category === cat.value && styles.pickerOptionTextSelected
                    ]}>
                      {cat.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Tipo de Producto *</Text>
            <TextInput
              style={[styles.input, errors.productType && styles.inputError]}
              value={productType}
              onChangeText={setProductType}
              placeholder="Ej: Tinte, Oxidante, Champú"
            />
            {errors.productType && <Text style={styles.errorText}>{errors.productType}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Línea (Opcional)</Text>
            <TextInput
              style={styles.input}
              value={line}
              onChangeText={setLine}
              placeholder="Ej: Illumina Color, Koleston Perfect"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Nombre Producto (con Tono/Ref)</Text>
            <TextInput
              style={styles.input}
              value={productName}
              onChangeText={setProductName}
              placeholder="Ej: Koleston 8/0 Rubio Claro"
            />
          </View>

          <View style={styles.rowContainer}>
            <View style={[styles.formGroup, styles.rowItem]}>
              <Text style={styles.label}>Cantidad Actual</Text>
              <TextInput
                style={styles.input}
                value={quantity}
                onChangeText={setQuantity}
                placeholder="Ej: 5"
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.formGroup, styles.rowItem]}>
              <Text style={styles.label}>Unidad de Medida</Text>
              <TextInput
                style={styles.input}
                value={unit}
                onChangeText={setUnit}
                placeholder="Ej: ml, g, unidades"
              />
            </View>
          </View>

          <View style={styles.rowContainer}>
            <View style={[styles.formGroup, styles.rowItem]}>
              <Text style={styles.label}>Tamaño del Envase *</Text>
              <TextInput
                style={[styles.input, errors.size && styles.inputError]}
                value={size}
                onChangeText={setSize}
                placeholder="Ej: 60"
                keyboardType="numeric"
              />
              {errors.size && <Text style={styles.errorText}>{errors.size}</Text>}
            </View>

            <View style={[styles.formGroup, styles.rowItem]}>
              <Text style={styles.label}>Umbral Bajo Stock</Text>
              <TextInput
                style={styles.input}
                value={lowStockThreshold}
                onChangeText={setLowStockThreshold}
                placeholder="Ej: 2"
                keyboardType="numeric"
              />
            </View>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Precio de Compra *</Text>
            <TextInput
              style={[styles.input, errors.purchasePrice && styles.inputError]}
              value={purchasePrice}
              onChangeText={setPurchasePrice}
              placeholder="Ej: 25.50"
              keyboardType="decimal-pad"
            />
            {errors.purchasePrice && <Text style={styles.errorText}>{errors.purchasePrice}</Text>}
            {purchasePrice && size && (
              <Text style={styles.helperText}>
                Costo por {unit}: ${(parseFloat(purchasePrice) / parseFloat(size)).toFixed(3)}
              </Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Código Barras EAN (Opcional)</Text>
            <TextInput
              style={styles.input}
              value={barcode}
              onChangeText={setBarcode}
              placeholder="Ej: 8005610433172"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.cancelButton} 
              onPress={() => router.back()}
              disabled={isLoading}
            >
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.saveButton, isLoading && styles.saveButtonDisabled]} 
              onPress={handleSave}
              disabled={isLoading}
            >
              <Text style={styles.saveButtonText}>
                {isLoading ? "Guardando..." : "Añadir Artículo"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  placeholder: {
    width: 34, // Same width as back button for centering
  },
  formContainer: {
    padding: spacing.md,
  },
  aiSection: {
    backgroundColor: Colors.light.card,
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.lg,
    ...shadows.sm,
  },
  aiHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  aiTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    marginLeft: spacing.sm,
    color: Colors.light.text,
  },
  aiInput: {
    marginBottom: spacing.sm,
  },
  aiButton: {
    backgroundColor: Colors.light.secondary,
    borderRadius: radius.sm,
    paddingVertical: spacing.sm,
    alignItems: "center",
    ...shadows.sm,
  },
  aiButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    marginBottom: spacing.md,
    color: Colors.light.text,
  },
  formGroup: {
    marginBottom: spacing.md,
  },
  label: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    marginBottom: spacing.sm,
    color: Colors.light.text,
  },
  input: {
    backgroundColor: Colors.light.card,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: radius.sm,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm + 2,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  inputError: {
    borderColor: Colors.light.error,
  },
  errorText: {
    color: Colors.light.error,
    fontSize: typography.sizes.xs,
    marginTop: spacing.xs,
  },
  rowContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: spacing.sm,
  },
  rowItem: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.lg,
    gap: spacing.sm,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: Colors.light.card,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: radius.sm,
    paddingVertical: spacing.md,
    alignItems: "center",
    ...shadows.sm,
  },
  cancelButtonText: {
    color: Colors.light.text,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  saveButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: radius.sm,
    paddingVertical: spacing.md,
    alignItems: "center",
    ...shadows.md,
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  pickerInput: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  pickerText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  pickerOptions: {
    backgroundColor: Colors.light.card,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: radius.sm,
    marginTop: spacing.xs,
    overflow: "hidden",
    ...shadows.sm,
  },
  pickerOption: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm + 2,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  pickerOptionText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  pickerOptionTextSelected: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  helperText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    marginTop: spacing.xs,
  },
});