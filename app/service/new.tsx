import React, { useState, useEffect, useRef } from "react";
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, TextInput, Platform, Alert, ActivityIndicator, Switch } from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import { ChevronLeft, Camera, Upload, ChevronRight, Shield, CheckCircle, AlertTriangle, Zap, Eye, EyeOff, History, TrendingUp, Package } from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
import Colors from "@/constants/colors";
import { mockClients } from "@/mocks/data";
import { useAIAnalysisStore } from "@/stores/ai-analysis-store";
import { useClientHistoryStore } from "@/stores/client-history-store";
import { usePhotoCaptureStore } from "@/stores/photo-capture-store";
import ClientHistoryPanel from "@/components/ClientHistoryPanel";
import DiagnosisSelector from "@/components/DiagnosisSelector";
import ZoneDiagnosisForm from "@/components/ZoneDiagnosisForm";
import Toast from "@/components/Toast";
import PhotoGallery from "@/components/PhotoGallery";
import GuidedCamera from "@/components/GuidedCamera";
import DesiredPhotoGallery from "@/components/DesiredPhotoGallery";
import DesiredColorAnalysisForm from "@/components/DesiredColorAnalysisForm";
import ViabilityIndicator from "@/components/ViabilityIndicator";
import FormulaCostBreakdown from "@/components/FormulaCostBreakdown";
import { BrandSelectionModal } from "@/components/BrandSelectionModal";
import FormulaVisualization from "@/components/formulation/FormulaVisualization";
import { ViabilityAnalysis, FormulaCost, ColorFormula } from "@/types/formulation";
import { InventoryConsumptionService } from "@/services/inventoryConsumptionService";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import { useInventoryStore } from "@/stores/inventory-store";
import { ColorCorrectionService } from "@/services/colorCorrectionService";
import { brandConversionService } from "@/services/brandConversionService";
import { parseFormulaText, calculateSimpleFormulaCost } from "@/utils/parseFormula";
import { MockFormulationService } from "@/services/mockFormulationService";
import {
  HairZone,
  HairThickness,
  HairDensity,
  NaturalTone,
  Undertone,
  UnwantedTone,
  ZoneColorAnalysis,
  ZonePhysicalAnalysis,
  HairDiagnosis,
  getHairThicknessOptions,
  getHairDensityOptions,
  getNaturalToneOptions,
  getUndertoneOptions
} from "@/types/hair-diagnosis";
import { CapturedPhoto, PhotoAngle, PhotoQuality, PHOTO_GUIDES } from "@/types/photo-capture";
import { DesiredPhoto, DesiredPhotoType, COLOR_TECHNIQUES, ColorTechnique, DESIRED_PHOTO_GUIDES, DesiredPhotoGuide } from "@/types/desired-photo";
import { MaintenanceLevel, BudgetLevel } from "@/types/lifestyle-preferences";
import { 
  DesiredColorAnalysisResult, 
  DesiredCaptureStep, 
  DESIRED_CAPTURE_GUIDES 
} from "@/types/desired-analysis";

// Define the steps of the consultation flow
const STEPS = [
  { id: "diagnosis", title: "Diagnóstico Capilar" },
  { id: "desired", title: "Resultado Deseado" },
  { id: "formulation", title: "Formulación" },
  { id: "result", title: "Resultado Final" }
];

interface ImageQualityCheck {
  isGoodLighting: boolean;
  isInFocus: boolean;
  hasGoodResolution: boolean;
  overallScore: number;
}

interface AIAnalysisResult {
  currentLevel: string;
  grayPercentage: string;
  hairTexture: string;
  hairPorosity: string;
  hairDensity: string;
  hairElasticity: string;
  hairResistance: string;
  undertones: string;
  damageLevel: string;
  recommendations: string[];
  confidence: number;
}

export default function NewServiceScreen() {
  const { clientId } = useLocalSearchParams();
  const [client, setClient] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [diagnosisMethod, setDiagnosisMethod] = useState("ai"); // "manual" or "ai"
  const [diagnosisTab, setDiagnosisTab] = useState("roots"); // "roots", "mids", "ends"
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [isDataFromAI, setIsDataFromAI] = useState(false);
  
  // New UI states
  
  // Track if component is mounted
  const isMounted = useRef(true);
  
  // Track desired capture step without causing re-renders
  const currentDesiredCaptureStepRef = useRef<DesiredCaptureStep>(DesiredCaptureStep.OVERALL);
  const currentDesiredPhotoTypeRef = useRef<DesiredPhotoType>(DesiredPhotoType.MAIN);
  
  // AI Analysis Store
  const { 
    isAnalyzing, 
    analysisResult, 
    privacyMode, 
    analyzeImage, 
    setPrivacyMode,
    clearAnalysis 
  } = useAIAnalysisStore();

  // Client History Store
  const {
    getClientProfile,
    getRecommendationsForClient,
    getWarningsForClient,
    getCompatibleFormulas,
    addHairEvolution,
    addPreviousFormula,
    initializeClientProfile,
    addConsentRecord
  } = useClientHistoryStore();
  
  // Diagnosis data
  const [hairPhotos, setHairPhotos] = useState<CapturedPhoto[]>([]);
  const [showGuidedCamera, setShowGuidedCamera] = useState(false);
  const [currentPhotoAngle, setCurrentPhotoAngle] = useState<PhotoAngle>(PhotoAngle.CROWN);
  const [cameraMode, setCameraMode] = useState<'diagnosis' | 'desired' | null>(null);
  const [cameraActive, setCameraActive] = useState(false);
  const [imageQuality, setImageQuality] = useState<ImageQualityCheck | null>(null);
  const [isImageProcessing, setIsImageProcessing] = useState(false);
  
  // General characteristics
  const [hairThickness, setHairThickness] = useState<string>("");
  const [hairDensity, setHairDensity] = useState<string>("");
  const [overallTone, setOverallTone] = useState<string>("");
  const [overallUndertone, setOverallUndertone] = useState<string>("");
  
  // Zone-based analysis
  const [zoneColorAnalysis, setZoneColorAnalysis] = useState<Record<HairZone, Partial<ZoneColorAnalysis>>>({
    [HairZone.ROOTS]: { zone: HairZone.ROOTS },
    [HairZone.MIDS]: { zone: HairZone.MIDS },
    [HairZone.ENDS]: { zone: HairZone.ENDS }
  });
  
  const [zonePhysicalAnalysis, setZonePhysicalAnalysis] = useState<Record<HairZone, Partial<ZonePhysicalAnalysis>>>({
    [HairZone.ROOTS]: { zone: HairZone.ROOTS },
    [HairZone.MIDS]: { zone: HairZone.MIDS },
    [HairZone.ENDS]: { zone: HairZone.ENDS }
  });
  
  // Chemical process history
  const [lastChemicalProcessType, setLastChemicalProcessType] = useState("");
  const [lastChemicalProcessDate, setLastChemicalProcessDate] = useState("");
  const [homeRemedies, setHomeRemedies] = useState(false); // Added for wizard
  
  // Hair measurements
  const [totalHairLength, setTotalHairLength] = useState("");
  const [monthlyGrowth, setMonthlyGrowth] = useState("1.25"); // Default average growth cm/month
  
  const [diagnosisNotes, setDiagnosisNotes] = useState("");
  
  // Desired result data
  const [desiredPhotos, setDesiredPhotos] = useState<DesiredPhoto[]>([]);
  const [desiredAnalysisResult, setDesiredAnalysisResult] = useState<DesiredColorAnalysisResult | null>(null);
  const [isAnalyzingDesired, setIsAnalyzingDesired] = useState(false);
  const [desiredNotes, setDesiredNotes] = useState("");
  const [desiredMethod, setDesiredMethod] = useState("ai"); // "manual" or "ai"
  // Removed pendingCameraOpen state - now opening camera directly
  
  // Platform detection for guided camera support
  const isIOSPlatform = Platform.OS === 'ios';
  const supportsGuidedCamera = !isIOSPlatform; // Disable guided camera on iOS due to expo-camera bug
  
  // Formulation data
  const [selectedBrand, setSelectedBrand] = useState("Wella Professionals");
  const [selectedLine, setSelectedLine] = useState("Illumina Color");
  const [formula, setFormula] = useState("");
  const [isGeneratingFormula, setIsGeneratingFormula] = useState(false);
  
  // Brand conversion states
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [brandModalType, setBrandModalType] = useState<'main' | 'conversion'>('main');
  const [conversionMode, setConversionMode] = useState(false);
  const [originalBrand, setOriginalBrand] = useState("");
  const [originalLine, setOriginalLine] = useState("");
  const [originalFormula, setOriginalFormula] = useState("");
  // Removed conversionReason - no longer needed after UX simplification
  const [formulaCost, setFormulaCost] = useState<FormulaCost | null>(null);
  const [viabilityAnalysis, setViabilityAnalysis] = useState<ViabilityAnalysis | null>(null);
  const [stockValidation, setStockValidation] = useState<{
    isChecking: boolean;
    hasStock: boolean;
    missingProducts: string[];
    checked: boolean;
  }>({
    isChecking: false,
    hasStock: true,
    missingProducts: [],
    checked: false,
  });
  
  // Final result data
  const [resultImage, setResultImage] = useState<string | null>(null);
  const [clientSatisfaction, setClientSatisfaction] = useState(5);
  const [resultNotes, setResultNotes] = useState("");
  const [consumeInventory, setConsumeInventory] = useState(false);
  const [isConsumingInventory, setIsConsumingInventory] = useState(false);

  // Client history data
  const [clientProfile, setClientProfile] = useState<any>(null);
  
  // Auto-save state
  
  // Prepare diagnosis data for auto-save
  const diagnosisData = {
    hairThickness,
    hairDensity,
    overallTone,
    overallUndertone,
    lastChemicalProcessType,
    lastChemicalProcessDate,
    diagnosisNotes,
    zoneColorAnalysis,
    zonePhysicalAnalysis,
    hairPhotos: hairPhotos.map(p => ({ id: p.id, angle: p.angle, uri: p.uri })), // Don't save full photo data
  };
  
  // Auto-save hook - REMOVED
//   // const { clear: clearAutoSave } = useAutoSave({
//     key: `diagnosis-draft-${clientId || 'new'}`,
//     data: diagnosisData,
//     delay: 2000, // Save after 2 seconds of no changes
//     enabled: currentStep === 0, // Only auto-save on diagnosis step
//     onSave: () => {
//       setLastSavedTime(new Date());
//       console.log('[AutoSave] Diagnosis saved');
//     },
//     onRestore: (savedData) => {
//       console.log('[AutoSave] Restoring diagnosis data');
//       // Restore basic fields
//       if (savedData.hairThickness) setHairThickness(savedData.hairThickness);
//       if (savedData.hairDensity) setHairDensity(savedData.hairDensity);
//       if (savedData.overallTone) setOverallTone(savedData.overallTone);
//       if (savedData.overallUndertone) setOverallUndertone(savedData.overallUndertone);
//       if (savedData.lastChemicalProcessType) setLastChemicalProcessType(savedData.lastChemicalProcessType);
//       if (savedData.lastChemicalProcessDate) setLastChemicalProcessDate(savedData.lastChemicalProcessDate);
//       if (savedData.diagnosisNotes) setDiagnosisNotes(savedData.diagnosisNotes);
//       
//       // Restore zone analysis
//       if (savedData.zoneColorAnalysis) setZoneColorAnalysis(savedData.zoneColorAnalysis);
//       if (savedData.zonePhysicalAnalysis) setZonePhysicalAnalysis(savedData.zonePhysicalAnalysis);
//       
//       // Note: Photos are not restored as URIs may be invalid after app restart
//       setToastMessage('📋 Datos restaurados del borrador guardado');
//       setShowToast(true);
//     },
//     onError: (error) => {
//       console.error('[AutoSave] Error:', error);
//     },
//   });
  const [showHistoryPanel, setShowHistoryPanel] = useState(true);

  // Load client data if clientId is provided
  // Cleanup effect
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // NOTE: Removed useEffect for pendingCameraOpen as we now open camera directly
  // This prevents race conditions and synchronization issues
  
  // Get photo capture store
  const { pendingPhoto, clearPendingPhoto } = usePhotoCaptureStore();
  
  // Handle pending photos from desired-camera screen
  useEffect(() => {
    if (pendingPhoto && pendingPhoto.photoType !== undefined) {
      console.log('[NewService] Processing pending photo from desired-camera');
      
      const newPhoto: DesiredPhoto = {
        id: Date.now().toString(),
        uri: pendingPhoto.uri,
        type: pendingPhoto.photoType === 0 ? DesiredPhotoType.MAIN : 
              pendingPhoto.photoType === 1 ? DesiredPhotoType.DETAILS : 
              DesiredPhotoType.ALTERNATIVE,
        timestamp: new Date()
      };
      
      setDesiredPhotos(prev => [...prev, newPhoto]);
      setToastMessage("✅ Foto capturada correctamente");
      setShowToast(true);
      
      // Clear the pending photo
      clearPendingPhoto();
    }
  }, [pendingPhoto]);
  
  useEffect(() => {
    console.log('[NewService] Client loading effect triggered');
    console.log('[NewService] clientId:', clientId);
    console.log('[NewService] typeof clientId:', typeof clientId);
    
    if (clientId) {
      const foundClient = mockClients.find(c => c.id === clientId);
      if (foundClient) {
        setClient(foundClient);
        console.log('[NewService] Client found:', foundClient.name);
        
        // Initialize client profile
        initializeClientProfile(clientId as string);
        const profile = getClientProfile(clientId as string);
        setClientProfile(profile);

        // Pre-fill some data based on client history
        if (profile && profile.hairEvolution.length > 0) {
          const lastEvolution = profile.hairEvolution[0];
          
          // Pre-fill zone analysis with last known values
          const defaultPhysicalAnalysis = {
            porosity: lastEvolution.porosity as any,
            resistance: lastEvolution.resistance as any,
            damage: lastEvolution.damage as any
          };
          
          setZonePhysicalAnalysis({
            [HairZone.ROOTS]: { zone: HairZone.ROOTS, ...defaultPhysicalAnalysis },
            [HairZone.MIDS]: { zone: HairZone.MIDS, ...defaultPhysicalAnalysis },
            [HairZone.ENDS]: { zone: HairZone.ENDS, ...defaultPhysicalAnalysis }
          });
          
          // Pre-fill color analysis with level
          const depthLevel = parseInt(lastEvolution.level) || 5;
          setZoneColorAnalysis(prev => ({
            [HairZone.ROOTS]: { ...prev[HairZone.ROOTS], depthLevel },
            [HairZone.MIDS]: { ...prev[HairZone.MIDS], depthLevel },
            [HairZone.ENDS]: { ...prev[HairZone.ENDS], depthLevel }
          }));
        }

        // Set preferred brand if available
        if (profile && profile.preferredBrands.length > 0) {
          setSelectedBrand(profile.preferredBrands[0]);
        }
      }
      
      // Register if safety verification was skipped
      const salonConfig = useSalonConfigStore.getState();
      if (salonConfig.skipSafetyVerification && clientId) {
        // Create a consent record indicating safety verification was skipped
        addConsentRecord(clientId as string, {
          id: Date.now().toString(),
          date: new Date().toLocaleDateString(),
          consentItems: [],
          signature: "SKIPPED",
          safetyChecklist: [],
          ipAddress: "***********", // In real app, get actual IP
          userAgent: "Salonier Mobile App",
          skipSafetyVerification: true
        });
      }
    }
  }, [clientId, getClientProfile, initializeClientProfile, addConsentRecord]);

  // Recalculate viability when desired analysis changes
  useEffect(() => {
    if (desiredAnalysisResult && validateDiagnosis()) {
      const viability = analyzeViability();
      setViabilityAnalysis(viability);
    }
  }, [desiredAnalysisResult]);

  // Update all fields when AI analysis completes
  useEffect(() => {
    console.log("useEffect triggered, analysisResult:", analysisResult);
    if (analysisResult) {
      console.log("Updating fields with AI results...");
      
      // Update general characteristics - use the string values directly
      setHairThickness(analysisResult.hairThickness);
      setHairDensity(analysisResult.hairDensity);
      setOverallTone(analysisResult.overallTone);
      setOverallUndertone(analysisResult.overallUndertone);
      
      console.log("General characteristics updated:", {
        hairThickness: analysisResult.hairThickness,
        hairDensity: analysisResult.hairDensity,
        overallTone: analysisResult.overallTone,
        overallUndertone: analysisResult.overallUndertone
      });
      
      // Update chemical process if detected
      if (analysisResult.detectedChemicalProcess) {
        setLastChemicalProcessType(analysisResult.detectedChemicalProcess);
        setLastChemicalProcessDate(analysisResult.estimatedLastProcessDate || "");
      }
      
      // Update home remedies if detected
      if (analysisResult.detectedHomeRemedies !== undefined) {
        setHomeRemedies(analysisResult.detectedHomeRemedies);
      }
      
      // Update all zones at once
      const newColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>> = {
        [HairZone.ROOTS]: {
          zone: HairZone.ROOTS,
          depthLevel: analysisResult.zoneAnalysis.roots.depthLevel,
          tone: analysisResult.zoneAnalysis.roots.tone as any,
          undertone: analysisResult.zoneAnalysis.roots.undertone as any,
          state: analysisResult.zoneAnalysis.roots.state as any,
          grayPercentage: analysisResult.zoneAnalysis.roots.grayPercentage,
          grayType: analysisResult.zoneAnalysis.roots.grayType as any,
          grayPattern: analysisResult.zoneAnalysis.roots.grayPattern as any,
          unwantedTone: analysisResult.zoneAnalysis.roots.unwantedTone as any,
          cuticleState: analysisResult.zoneAnalysis.roots.cuticleState as any
        },
        [HairZone.MIDS]: {
          zone: HairZone.MIDS,
          depthLevel: analysisResult.zoneAnalysis.mids.depthLevel,
          tone: analysisResult.zoneAnalysis.mids.tone as any,
          undertone: analysisResult.zoneAnalysis.mids.undertone as any,
          state: analysisResult.zoneAnalysis.mids.state as any,
          unwantedTone: analysisResult.zoneAnalysis.mids.unwantedTone as any,
          pigmentAccumulation: analysisResult.zoneAnalysis.mids.pigmentAccumulation as any,
          cuticleState: analysisResult.zoneAnalysis.mids.cuticleState as any,
          demarkationBands: analysisResult.zoneAnalysis.mids.demarkationBands
        },
        [HairZone.ENDS]: {
          zone: HairZone.ENDS,
          depthLevel: analysisResult.zoneAnalysis.ends.depthLevel,
          tone: analysisResult.zoneAnalysis.ends.tone as any,
          undertone: analysisResult.zoneAnalysis.ends.undertone as any,
          state: analysisResult.zoneAnalysis.ends.state as any,
          unwantedTone: analysisResult.zoneAnalysis.ends.unwantedTone as any,
          pigmentAccumulation: analysisResult.zoneAnalysis.ends.pigmentAccumulation as any,
          cuticleState: analysisResult.zoneAnalysis.ends.cuticleState as any
        }
      };
      
      const newPhysicalAnalysis: Record<HairZone, Partial<ZonePhysicalAnalysis>> = {
        [HairZone.ROOTS]: {
          zone: HairZone.ROOTS,
          porosity: analysisResult.zoneAnalysis.roots.porosity as any,
          elasticity: analysisResult.zoneAnalysis.roots.elasticity as any,
          resistance: analysisResult.zoneAnalysis.roots.resistance as any,
          damage: analysisResult.zoneAnalysis.roots.damage as any
        },
        [HairZone.MIDS]: {
          zone: HairZone.MIDS,
          porosity: analysisResult.zoneAnalysis.mids.porosity as any,
          elasticity: analysisResult.zoneAnalysis.mids.elasticity as any,
          resistance: analysisResult.zoneAnalysis.mids.resistance as any,
          damage: analysisResult.zoneAnalysis.mids.damage as any
        },
        [HairZone.ENDS]: {
          zone: HairZone.ENDS,
          porosity: analysisResult.zoneAnalysis.ends.porosity as any,
          elasticity: analysisResult.zoneAnalysis.ends.elasticity as any,
          resistance: analysisResult.zoneAnalysis.ends.resistance as any,
          damage: analysisResult.zoneAnalysis.ends.damage as any
        }
      };
      
      setZoneColorAnalysis(newColorAnalysis);
      setZonePhysicalAnalysis(newPhysicalAnalysis);
      
      // Mark that data is from AI
      setIsDataFromAI(true);
    }
  }, [analysisResult]);

  // Effect to recalculate cost when formula changes
  useEffect(() => {
    if (!formula || formula.trim() === '') {
      setFormulaCost(null);
      return;
    }

    // Debounce to avoid excessive calculations while typing
    const timeoutId = setTimeout(() => {
      calculateFormulaCost(formula).then(cost => {
        console.log('[useEffect - Formula change] Formula cost calculated:', cost);
        setFormulaCost(cost);
      }).catch(error => {
        console.error('[useEffect - Formula change] Error calculating formula cost:', error);
        // Use simple calculation as fallback
        const simpleCost = calculateSimpleFormulaCost(formula);
        setFormulaCost(simpleCost);
      });
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [formula]);

  const performImageQualityCheck = (imageUri: string): ImageQualityCheck => {
    // Simulate on-device image quality analysis
    // In real implementation, this would use actual image processing
    const mockQuality: ImageQualityCheck = {
      isGoodLighting: Math.random() > 0.3,
      isInFocus: Math.random() > 0.2,
      hasGoodResolution: Math.random() > 0.1,
      overallScore: Math.random() * 100
    };
    
    return mockQuality;
  };

  const takePhoto = async (setImageFunc: (uri: string) => void) => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permisos necesarios', 'Se necesitan permisos de cámara para tomar fotos');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        quality: 0.5, // Reducido temporalmente para evitar problemas de memoria
        allowsEditing: false,
        base64: false, // No cargar base64 para ahorrar memoria
      });

      if (!result.canceled && result.assets[0]) {
        setImageFunc(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error al tomar foto:', error);
      Alert.alert(
        'Error', 
        'No se pudo acceder a la cámara. Por favor, usa la galería mientras resolvemos este problema.',
        [
          { text: 'Usar Galería', onPress: () => pickImage(setImageFunc) },
          { text: 'Cancelar' }
        ]
      );
    }
  };

  const pickImage = async (setImageFunc: (uri: string) => void) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.5, // Reducido para consistencia
        allowsEditing: false,
        base64: false,
      });

      if (!result.canceled && result.assets[0]) {
        setImageFunc(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error al seleccionar imagen:', error);
      Alert.alert('Error', 'No se pudo acceder a la galería');
    }
  };

  const pickMultipleImages = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.5, // Reducido para evitar problemas de memoria
        selectionLimit: 5 - hairPhotos.length,
        base64: false,
      });

      if (!result.canceled && result.assets) {
      const newPhotos: CapturedPhoto[] = result.assets.map((asset, index) => {
        const quality = performImageQualityCheck(asset.uri);
        const qualityObj: PhotoQuality = {
          lighting: quality.isGoodLighting ? 'good' : quality.overallScore > 60 ? 'fair' : 'poor',
          focus: quality.isInFocus ? 'good' : 'fair',
          stability: 'good', // Assume good for uploaded photos
          overall: quality.overallScore
        };
        
        // Try to guess angle based on existing photos
        const capturedAngles = hairPhotos.map(p => p.angle);
        const availableGuides = PHOTO_GUIDES.filter(g => !capturedAngles.includes(g.angle));
        const angle = availableGuides[index]?.angle || PhotoAngle.CROWN;
        
        return {
          id: Date.now().toString() + index,
          uri: asset.uri,
          angle,
          quality: qualityObj,
          timestamp: new Date()
        };
      });
      
      setHairPhotos(prev => [...prev, ...newPhotos]);
      
      if (newPhotos.some(p => p.quality.overall < 60)) {
        setToastMessage("⚠️ Algunas fotos tienen calidad mejorable");
        setShowToast(true);
      }
    }
    } catch (error) {
      console.error('Error al seleccionar múltiples imágenes:', error);
      Alert.alert('Error', 'No se pudieron cargar las imágenes');
    }
  };

  const handleCameraCapture = (uri: string, angle: PhotoAngle, quality: PhotoQuality) => {
    const newPhoto: CapturedPhoto = {
      id: Date.now().toString(),
      uri,
      angle,
      quality,
      timestamp: new Date()
    };
    
    setHairPhotos(prev => {
      // Replace photo if same angle exists
      const filtered = prev.filter(p => p.angle !== angle);
      return [...filtered, newPhoto];
    });
    
    // Check if there are more required photos to capture
    const capturedAngles = [...hairPhotos.map(p => p.angle), angle];
    const nextRequiredGuide = PHOTO_GUIDES.find(g => 
      g.required && !capturedAngles.includes(g.angle)
    );
    
    if (nextRequiredGuide) {
      // Continue to next required photo
      setCurrentPhotoAngle(nextRequiredGuide.angle);
    } else {
      // All required photos captured
      setShowGuidedCamera(false);
      setCameraActive(false); // v10: Reset camera active state
      setToastMessage("✅ Fotos capturadas correctamente");
      setShowToast(true);
    }
  };

  const handleDesiredCameraCapture = (uri: string, angle: PhotoAngle, quality: PhotoQuality) => {
    // Add the photo as desired photo using the stored type
    let photoType = currentDesiredPhotoTypeRef.current;
    
    // If no type specified, assign based on current count
    if (!photoType) {
      if (desiredPhotos.length === 0) {
        photoType = DesiredPhotoType.MAIN;
      } else if (desiredPhotos.length === 1) {
        photoType = DesiredPhotoType.DETAILS;
      } else {
        // All additional photos are ALTERNATIVE
        photoType = DesiredPhotoType.ALTERNATIVE;
      }
    }
    
    const newPhoto: DesiredPhoto = {
      id: Date.now().toString(),
      uri,
      type: photoType,
      timestamp: new Date()
    };
    
    setDesiredPhotos(prev => {
      // For the first 3 photos, replace if same type exists
      // For additional photos (4-5), just add them
      if (photoType && prev.length < 3) {
        // Replace if same type exists for main types
        const filtered = prev.filter(p => p.type !== photoType);
        return [...filtered, newPhoto];
      } else {
        // Just add for additional photos
        return [...prev, newPhoto];
      }
    });
    
    // Close the camera
    setShowGuidedCamera(false);
    setCameraActive(false);
    setCameraMode(null);
    
    setToastMessage("✅ Foto capturada correctamente");
    setShowToast(true);
  };

  const handleAIAnalysis = async () => {
    if (hairPhotos.length === 0) {
      Alert.alert("Error", "Por favor captura al menos una foto del cabello");
      return;
    }
    
    const requiredPhotos = PHOTO_GUIDES.filter(g => g.required);
    const capturedRequiredAngles = hairPhotos.map(p => p.angle);
    const missingRequired = requiredPhotos.filter(g => !capturedRequiredAngles.includes(g.angle));
    
    if (missingRequired.length > 0) {
      Alert.alert(
        "Fotos requeridas faltantes",
        `Por favor captura las siguientes vistas: ${missingRequired.map(g => g.label).join(', ')}`,
        [
          { text: "Continuar capturando", style: "cancel" },
          { text: "Analizar con fotos actuales", onPress: () => performAnalysis() }
        ]
      );
      return;
    }

    if (imageQuality && imageQuality.overallScore < 40) {
      Alert.alert(
        "Calidad insuficiente",
        "La calidad de la imagen es muy baja para un análisis preciso. Por favor toma una nueva foto con mejor iluminación.",
        [{ text: "OK" }]
      );
      return;
    }

    // Check client warnings before analysis
    if (clientId) {
      const warnings = getWarningsForClient(clientId as string);
      if (warnings.length > 0) {
        const criticalWarnings = warnings.filter(w => w.includes('SEVERA') || w.includes('alto riesgo'));
        if (criticalWarnings.length > 0) {
          Alert.alert(
            "⚠️ Advertencia Crítica",
            `Este cliente tiene alertas importantes:\n\n${criticalWarnings.join('\n')}\n\n¿Deseas continuar?`,
            [
              { text: "Cancelar", style: "cancel" },
              { text: "Continuar con precaución", onPress: () => performAnalysis() }
            ]
          );
          return;
        }
      }
    }

    performAnalysis();
  };

  const performAnalysis = async () => {
    try {
      console.log("Starting AI analysis with", hairPhotos.length, "photos");
      // For now, use the first photo for analysis
      // In production, you'd send all photos to the AI
      const primaryPhoto = hairPhotos.find(p => p.angle === PhotoAngle.CROWN) || hairPhotos[0];
      await analyzeImage(primaryPhoto.uri);
      console.log("AI analysis completed");
      
      // The useEffect will handle updating the fields
      // Show success toast after a short delay to ensure fields are updated
      setTimeout(() => {
        setToastMessage("✅ Análisis completo. Revisa y ajusta según tu criterio");
        setShowToast(true);
      }, 500);
    } catch (error) {
      console.error("Error in analysis:", error);
      Alert.alert(
        "Error en el análisis",
        "No se pudo completar el análisis. Por favor intenta nuevamente.",
        [{ text: "OK" }]
      );
    }
  };

  // Helper function to safely get angle for desired capture step
  const getDesiredCaptureAngle = (step: DesiredCaptureStep): PhotoAngle => {
    const angleMap: Record<DesiredCaptureStep, PhotoAngle> = {
      [DesiredCaptureStep.OVERALL]: PhotoAngle.FRONT,
      [DesiredCaptureStep.ROOTS_DETAIL]: PhotoAngle.CROWN,
      [DesiredCaptureStep.HIGHLIGHTS]: PhotoAngle.LEFT_SIDE,
      [DesiredCaptureStep.ENDS_DETAIL]: PhotoAngle.BACK
    };
    
    const angle = angleMap[step];
    
    if (!angle) {
      console.error(`[DesiredPhoto] Invalid capture step: ${step}, defaulting to FRONT`);
      return PhotoAngle.FRONT;
    }
    
    return angle;
  };

  // Removed getConversionMessage - no longer needed after UX simplification

  const generateFormulaWithAI = async () => {
    if (!analysisResult && hairPhotos.length === 0) {
      Alert.alert("Error", "Necesitas completar el diagnóstico antes de generar la fórmula");
      return;
    }

    if (desiredPhotos.length === 0 || !desiredAnalysisResult) {
      Alert.alert("Error", "Necesitas completar el análisis del color deseado antes de generar la fórmula");
      return;
    }
    
    // Additional validation for desired result structure
    if (!desiredAnalysisResult.general) {
      console.error('[generateFormulaWithAI] desiredAnalysisResult missing general property:', desiredAnalysisResult);
      Alert.alert("Error", "El análisis del color deseado está incompleto. Por favor, vuelve a analizarlo.");
      return;
    }

    setIsGeneratingFormula(true);
    
    // Get client history for better formula generation
    let historyContext = "";
    if (clientId) {
      const compatibleFormulas = getCompatibleFormulas(clientId as string);
      const recommendations = getRecommendationsForClient(clientId as string);
      
      if (compatibleFormulas.length > 0) {
        historyContext += `\nFórmulas exitosas anteriores:\n${compatibleFormulas.slice(0, 2).map(f => `- ${f.formula} (Satisfacción: ${f.satisfaction}/5)`).join('\n')}`;
      }
      
      if (recommendations.length > 0) {
        historyContext += `\nRecomendaciones basadas en historial:\n${recommendations.slice(0, 3).map(r => `- ${r}`).join('\n')}`;
      }
    }

    // Get current hair analysis summary
    const currentLevel = analysisResult?.zoneAnalysis?.roots?.depthLevel || 5;
    const targetLevel = parseInt(desiredAnalysisResult.general.overallLevel) || 8;
    const levelDifference = Math.abs(targetLevel - currentLevel);
    const selectedTechnique = desiredAnalysisResult.general.technique || 'full_color';
    
    // Analyze color correction needs
    const unwantedTones: Partial<Record<HairZone, UnwantedTone>> = {};
    Object.values(zoneColorAnalysis).forEach(zone => {
      if (zone.unwantedTone && zone.zone) {
        unwantedTones[zone.zone] = zone.unwantedTone;
      }
    });
    
    const correctionAnalysis = ColorCorrectionService.analyzeColorCorrection(
      zoneColorAnalysis as Record<HairZone, ZoneColorAnalysis>,
      desiredAnalysisResult,
      unwantedTones,
      selectedBrand,
      selectedLine
    );
    
    // Check if we should use the new regional formulation service
    const salonConfig = useSalonConfigStore.getState();
    console.log('[generateFormulaWithAI] Checking regional config:', {
      hasRegionalConfig: !!salonConfig.regionalConfig,
      hasAnalysisResult: !!analysisResult,
      hasDesiredResult: !!desiredAnalysisResult
    });
    
    if (salonConfig.regionalConfig && analysisResult && desiredAnalysisResult) {
      try {
        const formulaContext = {
          currentDiagnosis: analysisResult,
          desiredResult: desiredAnalysisResult,
          brand: selectedBrand,
          line: selectedLine,
          regionalConfig: salonConfig.regionalConfig,
          clientHistory: historyContext,
          conversionMode: conversionMode ? {
            originalBrand: originalBrand || '',
            originalLine: originalLine || '',
            originalFormula: originalFormula || ''
          } : undefined
        };
        
        const generatedFormula = await MockFormulationService.generateFormula(formulaContext);
        setFormula(generatedFormula);
        
        // Calculate formula cost for regional service
        calculateFormulaCost(generatedFormula).then(cost => {
          console.log('[generateFormulaWithAI - Regional] Formula cost calculated:', cost);
          console.log('[generateFormulaWithAI - Regional] Current inventory level:', useSalonConfigStore.getState().configuration.inventoryControlLevel);
          setFormulaCost(cost);
          setIsGeneratingFormula(false);
        }).catch(error => {
          console.error('[generateFormulaWithAI - Regional] Error calculating formula cost:', error);
          // Use simple calculation as fallback
          const simpleCost = calculateSimpleFormulaCost(generatedFormula);
          setFormulaCost(simpleCost);
          setIsGeneratingFormula(false);
        });
        
        // Analyze viability with the generated formula
        const viability = analyzeViability();
        setViabilityAnalysis(viability);
        
        return;
      } catch (error) {
        console.error('[generateFormulaWithAI] Error in regional formula generation:', error);
        // Show user-friendly error message
        const errorMessage = error instanceof Error ? error.message : 'Error al generar la fórmula';
        Alert.alert(
          'Error', 
          `No se pudo generar la fórmula: ${errorMessage}. Usando método alternativo.`,
          [{ text: 'OK' }]
        );
        // Fall back to original generation method
      }
    } else {
      console.log('[generateFormulaWithAI] Missing required data for regional service, using fallback');
    }
    
    // Original AI formula generation as fallback
    setTimeout(async () => {
      let formulaBase = "";
      let applicationSteps = "";
      let processingTime = 35;
      let conversionResult = null;
      
      // Add correction steps if needed
      const correctionSteps = correctionAnalysis.needsCorrection 
        ? ColorCorrectionService.formatCorrectionForFormula(correctionAnalysis)
        : "";

      // If in conversion mode, perform real conversion
      if (conversionMode && originalBrand && originalLine && originalFormula) {
        try {
          // Parse the original formula to extract tone
          const toneMatch = originalFormula.match(/(\d+[/.,-]?\d*)/);
          const originalTone = toneMatch ? toneMatch[1] : "7/1";
          
          // Create a mock ColorFormula object for conversion
          const originalColorFormula: ColorFormula = {
            brand: originalBrand,
            line: originalLine,
            colors: [{ tone: originalTone, amount: 60 }],
            developerVolume: originalFormula.includes("30") ? 30 : 20,
            developerRatio: originalFormula.includes("1:2") ? "1:2" : "1:1.5",
            processingTime: 35,
            additives: []
          };
          
          // Perform the conversion
          conversionResult = await brandConversionService.convert(
            originalColorFormula,
            selectedBrand,
            selectedLine
          );
          
          // Use converted formula as base
          const targetTone = conversionResult.targetFormula.colors[0].tone;
          const targetAmount = conversionResult.targetFormula.colors[0].amount;
          
          formulaBase = `Fórmula Convertida:
- ${selectedLine} ${targetTone} (${targetAmount}g)
- Oxidante ${conversionResult.targetFormula.developerVolume} vol (${
            conversionResult.targetFormula.developerRatio === "1:2" ? targetAmount * 2 : targetAmount * 1.5
          }g)`;
          
          processingTime = conversionResult.targetFormula.processingTime;
          
          applicationSteps = `Aplicación (adaptada de ${originalBrand}):
1. ${conversionResult.adjustments.mixRatio !== "Sin cambios" ? 
     `Mezclar en proporción ${conversionResult.adjustments.mixRatio.split(' → ')[1]}` : 
     "Mantener proporción original"}
2. Aplicar según técnica habitual
3. Tiempo de proceso: ${processingTime} minutos ${
     conversionResult.adjustments.processingTime !== 0 ? 
     `(${conversionResult.adjustments.processingTime > 0 ? '+' : ''}${conversionResult.adjustments.processingTime} min vs. original)` : 
     ''}`;
          
        } catch (error) {
          console.error('Error in brand conversion:', error);
          // Fallback to standard formula generation
          conversionResult = null;
        }
      }
      
      // If not in conversion mode or conversion failed, generate standard formula
      if (!conversionResult) {
        // Adjust formula based on technique
        if (selectedTechnique === 'balayage') {
          formulaBase = `Fórmula para Balayage:
- ${selectedLine} ${targetLevel}/1 (30g)
- ${selectedLine} ${targetLevel}/69 (10g)
- Oxidante 30 vol (40g) para medios/puntas
- Oxidante 20 vol (40g) para raíces`;
          
          applicationSteps = `Aplicación Balayage:
1. Seccionar el cabello en V
2. Aplicar con pincel desde medios hacia puntas
3. Dejar raíces naturales o aplicar fórmula más suave
4. Envolver en papel aluminio selectivamente`;
        } else if (selectedTechnique === 'highlights') {
          formulaBase = `Fórmula para Mechas:
- Polvo decolorante (30g)
- Oxidante 30 vol (60g)
- Olaplex N°1 (3.75ml)
Para tonalizar:
- ${selectedLine} ${targetLevel}/81 (20g)
- ${selectedLine} ${targetLevel}/69 (10g)
- Oxidante 10 vol (30g)`;
          
          applicationSteps = `Aplicación Mechas:
1. Seleccionar mechones finos con peine de cola
2. Aplicar decolorante desde raíz a puntas
3. Envolver en papel aluminio
4. Procesar hasta alcanzar fondo de aclaración deseado
5. Tonalizar según necesidad`;
        } else {
          formulaBase = `Fórmula Tinte Completo:
- ${selectedLine} ${targetLevel}/1 (${levelDifference > 2 ? '40g' : '30g'})
- ${selectedLine} ${targetLevel}/69 (${levelDifference > 2 ? '20g' : '10g'})
- Oxidante ${levelDifference > 2 ? '30' : '20'} vol (${levelDifference > 2 ? '90g' : '60g'})`;
          
          applicationSteps = `Aplicación:
1. Aplicar primero en raíces, dejar ${levelDifference > 2 ? '25' : '20'} minutos
2. Extender a medios y puntas por ${levelDifference > 2 ? '20' : '15'} minutos adicionales
3. Emulsionar con agua tibia antes de enjuagar`;
        }

        // Adjust processing time based on contrast level
        if (desiredAnalysisResult.advanced.contrast === 'high') {
          processingTime = 45;
        } else if (desiredAnalysisResult.advanced.contrast === 'subtle') {
          processingTime = 25;
        }
      }

      // Adjust total processing time if correction is needed
      const totalProcessingTime = processingTime + (correctionAnalysis.totalTime || 0);
      
      // Add conversion context if in conversion mode
      let conversionContext = "";
      if (conversionMode && originalBrand && originalLine && originalFormula) {
        conversionContext = `═══════════════════════════════════════
🔄 FÓRMULA TRADUCIDA
═══════════════════════════════════════
Objetivo: Mantener color actual
Original: ${originalBrand} ${originalLine} - ${originalFormula}
═══════════════════════════════════════

EQUIVALENCIA EN ${selectedBrand.toUpperCase()} ${selectedLine.toUpperCase()}:
`;
      }
      
      const mockFormula = `${conversionContext}${selectedBrand} ${selectedLine}:
${correctionSteps}
${correctionSteps ? `\nPASO ${correctionAnalysis.steps.length + 1} - ` : ''}FÓRMULA PRINCIPAL:
${formulaBase}

Tiempo de proceso${correctionSteps ? ' (fórmula principal)' : ''}: ${processingTime} minutos
${correctionSteps ? `Tiempo total del servicio: ${totalProcessingTime} minutos\n` : ''}
${applicationSteps}

Análisis de compatibilidad:
- Cambio de nivel: ${currentLevel} → ${targetLevel} (${levelDifference} niveles)
- Técnica seleccionada: ${COLOR_TECHNIQUES.find(t => t.id === selectedTechnique)?.name || 'Tinte completo'}
- Contraste deseado: ${desiredAnalysisResult.advanced.contrast === 'subtle' ? 'Sutil' : desiredAnalysisResult.advanced.contrast === 'medium' ? 'Medio' : 'Alto'}
- Dirección: ${desiredAnalysisResult.advanced.direction === 'warmer' ? 'Más cálido' : desiredAnalysisResult.advanced.direction === 'cooler' ? 'Más frío' : 'Neutral'}

Análisis por zonas:
- Raíces: Nivel ${desiredAnalysisResult.zones[HairZone.ROOTS].desiredLevel} - ${desiredAnalysisResult.zones[HairZone.ROOTS].desiredTone}
- Medios: Nivel ${desiredAnalysisResult.zones[HairZone.MIDS].desiredLevel} - ${desiredAnalysisResult.zones[HairZone.MIDS].desiredTone}
- Puntas: Nivel ${desiredAnalysisResult.zones[HairZone.ENDS].desiredLevel} - ${desiredAnalysisResult.zones[HairZone.ENDS].desiredTone}

Consideraciones especiales:
${analysisResult?.recommendations?.slice(0, 2).join('\n') || '- Cabello en buenas condiciones para el proceso'}
${levelDifference > 3 ? '\n- ⚠️ Cambio significativo: considerar proceso en varias sesiones' : ''}

${historyContext}${conversionMode && conversionResult ? `

═══════════════════════════════════════
AJUSTES Y RECOMENDACIONES:
═══════════════════════════════════════
• Conversión: ${conversionResult.adjustments.toneMapping}
• ${conversionResult.adjustments.mixRatio}
• Tiempo: ${conversionResult.adjustments.processingTime !== 0 ? 
    `${conversionResult.adjustments.processingTime > 0 ? '+' : ''}${conversionResult.adjustments.processingTime} minutos` : 
    'Mantener tiempo original'}
• Nivel de confianza: ${conversionResult.confidence}%

${conversionResult.adjustments.additionalNotes.map(note => `• ${note}`).join('\n')}

${conversionResult.warnings ? `⚠️ ADVERTENCIAS:
${conversionResult.warnings.map(warning => `- ${warning}`).join('\n')}` : ''}` : conversionMode ? `

═══════════════════════════════════════
AJUSTES Y RECOMENDACIONES:
═══════════════════════════════════════
• Base: ${targetLevel}/1 equivale a ${originalFormula.split(' ')[0]} en ${originalBrand}
• Matiz: Ajustado para compensar diferencias de pigmentación
• Oxidante: Mantener proporción ${levelDifference > 2 ? '1:1.5' : '1:1'}
• Tiempo: ${processingTime} min (puede variar ±5 min vs. original)

⚠️ IMPORTANTE:
- Realizar prueba de mechón obligatoria
- La conversión tiene ~85-90% de precisión
- Los pigmentos de ${selectedBrand} son ${Math.random() > 0.5 ? 'más concentrados' : 'más suaves'} que ${originalBrand}
- Ajustar tiempo según resultado de prueba` : ''}`;

      setFormula(mockFormula);
      
      // Calculate formula cost
      calculateFormulaCost(mockFormula).then(cost => {
        console.log('[generateUltraIntelligentFormula] Formula cost calculated:', cost);
        console.log('[generateUltraIntelligentFormula] Current inventory level:', useSalonConfigStore.getState().configuration.inventoryControlLevel);
        setFormulaCost(cost);
        setIsGeneratingFormula(false);
      }).catch(error => {
        console.error('Error calculating formula cost:', error);
        console.log('[generateUltraIntelligentFormula] Current inventory level on error:', useSalonConfigStore.getState().configuration.inventoryControlLevel);
        // Use simple calculation as fallback
        const simpleCost = calculateSimpleFormulaCost(mockFormula);
        setFormulaCost(simpleCost);
        setIsGeneratingFormula(false);
      });
    }, 2500);
  };

  const handleRecommendationApply = (recommendation: string) => {
    // Apply recommendation to current analysis
    if (recommendation.includes('Fórmula exitosa anterior')) {
      const brandMatch = recommendation.match(/(\w+\s+\w+)/);
      if (brandMatch) {
        setSelectedBrand(brandMatch[1]);
      }
    }
    
    Alert.alert(
      "Recomendación aplicada",
      `Se ha aplicado la recomendación: ${recommendation}`,
      [{ text: "OK" }]
    );
  };

  const validateDiagnosis = () => {
    // Check if all required fields are filled
    const hasGeneralData = hairThickness && hairDensity && overallTone && overallUndertone;
    const hasAllZoneData = Object.values(zoneColorAnalysis).every(zone => 
      zone.depthLevel && zone.tone && zone.undertone && zone.state
    ) && Object.values(zonePhysicalAnalysis).every(zone =>
      zone.porosity && zone.elasticity && zone.resistance && zone.damage
    );
    
    return hasGeneralData && hasAllZoneData;
  };

  const checkStockAvailability = async () => {
    const salonConfig = useSalonConfigStore.getState();
    
    // Only check stock if inventory control is enabled
    if (salonConfig.configuration.inventoryControlLevel !== 'control-total') {
      return;
    }
    
    if (!formula) {
      Alert.alert('Error', 'Primero debes generar una fórmula');
      return;
    }
    
    setStockValidation(prev => ({ ...prev, isChecking: true }));
    
    try {
      const colorFormula = parseFormulaText(formula);
      const stockCheck = await InventoryConsumptionService.checkStock(colorFormula);
      
      setStockValidation({
        isChecking: false,
        hasStock: stockCheck.hasStock,
        missingProducts: stockCheck.missingProducts,
        checked: true,
      });
      
      if (!stockCheck.hasStock) {
        Alert.alert(
          'Stock Insuficiente',
          `No hay stock suficiente para los siguientes productos:\n\n${stockCheck.missingProducts.join('\n')}`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error checking stock:', error);
      setStockValidation(prev => ({ ...prev, isChecking: false }));
      Alert.alert('Error', 'No se pudo verificar el stock');
    }
  };
  
  const analyzeViability = (): ViabilityAnalysis => {
    // Calculate level difference
    const currentLevel = parseInt(overallTone.split('/')[0]) || 5;
    const desiredLevelStr = desiredAnalysisResult?.general?.overallLevel || "7";
    const desiredLevel = parseInt(desiredLevelStr.split('/')[0]) || 7;
    const levelDifference = Math.abs(desiredLevel - currentLevel);

    // Assess hair health based on physical analysis
    // Map damage and porosity strings to numeric values
    const damageToNumber = (damage: string): number => {
      switch(damage) {
        case 'Bajo': return 1;
        case 'Medio': return 2;
        case 'Alto': return 3;
        case 'Muy Alto': return 4;
        default: return 2;
      }
    };
    
    const porosityToNumber = (porosity: string): number => {
      switch(porosity) {
        case 'Baja': return 1;
        case 'Media': return 2;
        case 'Alta': return 3;
        default: return 2;
      }
    };
    
    const avgDamage = Object.values(zonePhysicalAnalysis).reduce((sum, zone) => 
      sum + damageToNumber(zone.damage || 'Medio'), 0) / 3;
    const avgPorosity = Object.values(zonePhysicalAnalysis).reduce((sum, zone) => 
      sum + porosityToNumber(zone.porosity || 'Media'), 0) / 3;
    
    let hairHealth: 'good' | 'fair' | 'poor' = 'good';
    if (avgDamage >= 3.5 || avgPorosity >= 3) {
      hairHealth = 'poor';
    } else if (avgDamage >= 2.5 || avgPorosity >= 2.5) {
      hairHealth = 'fair';
    }

    // Check chemical history
    const clientHistoryData = clientId ? getClientProfile(clientId as string) : null;
    const recentChemicalProcesses = clientHistoryData?.hairEvolution
      ?.filter((h: any) => {
        const processDate = new Date(h.date);
        const threeMonthsAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        return processDate > threeMonthsAgo;
      }).length || 0;

    // Determine sessions needed
    let estimatedSessions = 1;
    if (levelDifference > 4 || hairHealth === 'poor') {
      estimatedSessions = Math.ceil(levelDifference / 2);
    } else if (levelDifference > 2) {
      estimatedSessions = 2;
    }

    // Calculate score
    let score: 'safe' | 'caution' | 'risky' = 'safe';
    const warnings: string[] = [];
    const recommendations: string[] = [];

    if (levelDifference > 5 || (levelDifference > 3 && hairHealth === 'poor')) {
      score = 'risky';
      warnings.push('Cambio de color muy drástico para el estado actual del cabello');
      warnings.push('Alto riesgo de daño estructural');
      recommendations.push('Considerar un proceso gradual en varias sesiones');
      recommendations.push('Aplicar tratamiento reconstructor previo');
    } else if (levelDifference > 3 || hairHealth === 'fair' || recentChemicalProcesses > 0) {
      score = 'caution';
      if (levelDifference > 3) {
        warnings.push(`Diferencia de ${levelDifference} niveles requiere decoloración`);
      }
      if (hairHealth === 'fair') {
        warnings.push('El cabello presenta daño moderado');
      }
      if (recentChemicalProcesses > 0) {
        warnings.push('Procesos químicos recientes detectados');
      }
      recommendations.push('Usar oxidante de menor volumen si es posible');
      recommendations.push('Aplicar protector durante el proceso');
      recommendations.push('Realizar prueba de mechón obligatoria');
    } else {
      recommendations.push('Proceso seguro con precauciones estándar');
      recommendations.push('Mantener tiempos de exposición recomendados');
    }

    // Add specific recommendations based on desired technique
    const technique = desiredAnalysisResult?.general?.technique;
    if (technique === 'balayage' || technique === 'highlights') {
      recommendations.push('Técnica parcial reduce el impacto en el cabello');
    }

    return {
      score,
      factors: {
        levelDifference,
        hairHealth,
        chemicalHistory: recentChemicalProcesses > 0 ? ['Procesos recientes'] : [],
        estimatedSessions
      },
      warnings,
      recommendations
    };
  };

  const calculateFormulaCost = async (formulaText: string): Promise<FormulaCost> => {
    const salonConfig = useSalonConfigStore.getState();
    const inventoryStore = useInventoryStore.getState();
    
    // Check if inventory control is enabled
    if (salonConfig.configuration.inventoryControlLevel === 'solo-formulas') {
      // Use simple cost calculation without inventory
      return calculateSimpleFormulaCost(formulaText);
    }
    
    // Parse formula text into structured data
    const colorFormula = parseFormulaText(formulaText);
    
    try {
      // Use inventory service for accurate cost calculation
      const consumptionAnalysis = await InventoryConsumptionService.calculateFormulationCost(colorFormula);
      
      const items: FormulaCost['items'] = consumptionAnalysis.items.map(item => ({
        product: item.productName,
        amount: `${item.amount}${item.unit}`,
        unitCost: item.unitCost,
        totalCost: item.totalCost
      }));
      
      const totalMaterialCost = consumptionAnalysis.totalCost;
      
      // Apply salon's configured markup
      const suggestedServicePrice = salonConfig.applyMarkup(totalMaterialCost);
      const profitMargin = suggestedServicePrice - totalMaterialCost;
      
      return {
        items,
        totalMaterialCost: Math.round(totalMaterialCost * 100) / 100,
        suggestedServicePrice: Math.round(suggestedServicePrice * 100) / 100,
        profitMargin: Math.round(profitMargin * 100) / 100
      };
    } catch (error) {
      console.error('Error calculating formula cost from inventory:', error);
      // Fallback to simple calculation
      return calculateSimpleFormulaCost(formulaText);
    }
  };
  
  // Helper function for simple cost calculation (no inventory)
  
  // Helper function to parse formula text into structured ColorFormula

  // Navigation helper function
  const navigateToFinish = () => {
    console.log('[NewService] navigateToFinish called');
    console.log('[NewService] isMounted:', isMounted.current);
    console.log('[NewService] clientId:', clientId);
    console.log('[NewService] typeof clientId:', typeof clientId);
    
    if (!isMounted.current) {
      console.log('[NewService] Component unmounted, skipping navigation');
      return;
    }
    
    try {
      // Check if router is available
      if (!router || typeof router.replace !== 'function') {
        console.error('[NewService] Router not available');
        throw new Error('Router not available');
      }
      
      // Validate clientId
      const hasValidClientId = clientId && 
                              typeof clientId === 'string' && 
                              clientId.trim() !== '' &&
                              clientId !== 'undefined' &&
                              clientId !== 'null';
      
      const destination = hasValidClientId 
        ? `/client/${clientId}` // Use absolute path
        : "/(tabs)";
      
      console.log('[NewService] Navigating to:', destination);
      
      // Use replace instead of push to avoid navigation stack issues
      router.replace(destination);
    } catch (error) {
      console.error('[NewService] Navigation error:', error);
      // Fallback navigation
      try {
        console.log('[NewService] Attempting fallback navigation to tabs');
        router.replace("/(tabs)");
      } catch (fallbackError) {
        console.error('[NewService] Fallback navigation also failed:', fallbackError);
        // Last resort - show error to user
        setToastMessage('❌ Error al navegar. Por favor vuelve manualmente.');
        setShowToast(true);
        // Reset loading state if navigation fails
        setIsConsumingInventory(false);
      }
    }
  };

  const nextStep = async () => {
    // If we're on diagnosis step, validate before continuing
    if (currentStep === 0 && !validateDiagnosis()) {
      Alert.alert(
        "Diagnóstico incompleto",
        "Por favor completa todos los campos requeridos antes de continuar.",
        [{ text: "OK" }]
      );
      return;
    }
    
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Save service data to client history
      console.log('[NewService] Saving service data...');
      console.log('[NewService] Current clientId:', clientId);
      
      try {
        if (clientId && typeof clientId === 'string' && clientId.trim() !== '') {
          // Get average values from zone analysis
          const avgDepthLevel = Math.round(
            Object.values(zoneColorAnalysis).reduce((sum, zone) => sum + (zone.depthLevel || 0), 0) / 3
          );
          
          const rootAnalysis = zonePhysicalAnalysis[HairZone.ROOTS];
          
          // Add hair evolution
          console.log('[NewService] Adding hair evolution...');
          addHairEvolution(clientId as string, {
            date: new Date().toLocaleDateString(),
            level: avgDepthLevel.toString(),
            porosity: rootAnalysis?.porosity || "Media",
            damage: rootAnalysis?.damage || "Medio",
            resistance: rootAnalysis?.resistance || "Media",
            notes: diagnosisNotes
          });

          // Add formula if completed
          if (formula) {
            console.log('[NewService] Adding formula to history...');
            addPreviousFormula(clientId as string, {
              id: Date.now().toString(),
              date: new Date().toLocaleDateString(),
              formula: formula,
              brand: selectedBrand,
              line: selectedLine,
              result: clientSatisfaction >= 4 ? 'excelente' : clientSatisfaction >= 3 ? 'bueno' : 'regular',
              satisfaction: clientSatisfaction,
              notes: resultNotes,
              processingTime: 35,
              oxidantVolume: "20 vol"
            });
          }
          console.log('[NewService] Service data saved successfully');
        } else {
          console.log('[NewService] No valid clientId, skipping history save');
        }
      } catch (error) {
        console.error('[NewService] Error saving service data:', error);
        // Continue with navigation even if saving fails
      }
      
      // Consume inventory if enabled
      if (consumeInventory && formula && useSalonConfigStore.getState().configuration.inventoryControlLevel === 'control-total') {
        setIsConsumingInventory(true);
        
        let successMessage = '';
        let errorMessage = '';
        
        try {
          const colorFormula = parseFormulaText(formula);
          
          // Validate parsed formula has at least some data
          if (colorFormula.colors.length === 0 && !colorFormula.developer) {
            throw new Error('No se pudo interpretar la fórmula correctamente');
          }
          
          const consumptionResult = await InventoryConsumptionService.consumeFormulation(
            Date.now().toString(), // formulationId
            colorFormula,
            client?.name || 'Cliente'
          );
          
          if (consumptionResult.success) {
            successMessage = 'Inventario actualizado correctamente';
            setToastMessage('✅ ' + successMessage);
            setShowToast(true);
          } else {
            errorMessage = consumptionResult.errors.join(', ');
            setToastMessage('⚠️ ' + errorMessage);
            setShowToast(true);
          }
        } catch (error) {
          console.error('Error consuming inventory:', error);
          errorMessage = error instanceof Error ? error.message : 'Error desconocido';
          setToastMessage('❌ Error: ' + errorMessage);
          setShowToast(true);
        } finally {
          setIsConsumingInventory(false);
        }
      }
      
      // Clear auto-save draft when service is completed
      // clearAutoSave();
      
      // Navigate immediately after saving
      navigateToFinish();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      try {
        router.back();
      } catch (error) {
        console.error('[NewService] Error navigating back:', error);
        // Fallback to tabs
        router.replace("/(tabs)");
      }
    }
  };
  
  // Calculate diagnosis progress
  const calculateDiagnosisProgress = () => {
    let totalFields = 0;
    let completedFields = 0;
    let requiredFields = 0;
    let completedRequiredFields = 0;
    
    // General characteristics (4 required fields)
    requiredFields += 4;
    totalFields += 6; // 4 required + 2 optional (chemical process)
    
    if (hairThickness) { completedFields++; completedRequiredFields++; }
    if (hairDensity) { completedFields++; completedRequiredFields++; }
    if (overallTone) { completedFields++; completedRequiredFields++; }
    if (overallUndertone) { completedFields++; completedRequiredFields++; }
    if (lastChemicalProcessType) completedFields++;
    if (lastChemicalProcessDate) completedFields++;
    
    // Zone analysis (per zone: 7 required color fields + 4 required physical fields)
    Object.values(HairZone).forEach(zone => {
      const colorAnalysis = zoneColorAnalysis[zone];
      const physicalAnalysis = zonePhysicalAnalysis[zone];
      
      // Color analysis fields
      requiredFields += 5; // depthLevel, tone, undertone, state, damage
      totalFields += 7; // + grayPercentage, previousChemical
      
      if (colorAnalysis.depthLevel) { completedFields++; completedRequiredFields++; }
      if (colorAnalysis.tone) { completedFields++; completedRequiredFields++; }
      if (colorAnalysis.undertone) { completedFields++; completedRequiredFields++; }
      if (colorAnalysis.state) { completedFields++; completedRequiredFields++; }
      if (colorAnalysis.damage) { completedFields++; completedRequiredFields++; }
      if (colorAnalysis.grayPercentage !== undefined) completedFields++;
      if (colorAnalysis.previousChemical) completedFields++;
      
      // Physical analysis fields
      requiredFields += 4; // porosity, elasticity, resistance, damage
      totalFields += 4;
      
      if (physicalAnalysis.porosity) { completedFields++; completedRequiredFields++; }
      if (physicalAnalysis.elasticity) { completedFields++; completedRequiredFields++; }
      if (physicalAnalysis.resistance) { completedFields++; completedRequiredFields++; }
      if (physicalAnalysis.damage) { completedFields++; completedRequiredFields++; }
    });
    
    // Photos (at least 3 required)
    requiredFields += 3;
    totalFields += 5;
    completedRequiredFields += Math.min(hairPhotos.length, 3);
    completedFields += hairPhotos.length;
    
    return {
      totalFields,
      completedFields,
      requiredFields,
      completedRequiredFields,
    };
  };
  

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      {STEPS.map((step, index) => (
        <React.Fragment key={step.id}>
          <View 
            style={[
              styles.progressStep, 
              index <= currentStep ? styles.progressStepActive : {}
            ]}
          >
            <Text style={styles.progressStepText}>{index + 1}</Text>
          </View>
          {index < STEPS.length - 1 && (
            <View 
              style={[
                styles.progressLine, 
                index < currentStep ? styles.progressLineActive : {}
              ]} 
            />
          )}
        </React.Fragment>
      ))}
    </View>
  );

  const renderPrivacyBanner = () => (
    <View style={styles.privacyBanner}>
      <Shield size={16} color={Colors.light.success} />
      <Text style={styles.privacyBannerText}>
        Política "Analizar y Descartar": Las imágenes se procesan y eliminan inmediatamente. 
        No se almacenan fotos permanentemente.
      </Text>
    </View>
  );

  const renderImageQualityIndicator = () => {
    if (!imageQuality) return null;

    const getQualityColor = (score: number) => {
      if (score >= 80) return Colors.light.success;
      if (score >= 60) return Colors.light.warning;
      return Colors.light.error;
    };

    const getQualityText = (score: number) => {
      if (score >= 80) return "Excelente";
      if (score >= 60) return "Buena";
      return "Mejorable";
    };

    return (
      <View style={styles.qualityIndicator}>
        <View style={styles.qualityHeader}>
          <Text style={styles.qualityTitle}>Calidad de imagen</Text>
          <View style={[
            styles.qualityBadge,
            { backgroundColor: getQualityColor(imageQuality.overallScore) + "20" }
          ]}>
            <Text style={[
              styles.qualityBadgeText,
              { color: getQualityColor(imageQuality.overallScore) }
            ]}>
              {getQualityText(imageQuality.overallScore)}
            </Text>
          </View>
        </View>
        
        <View style={styles.qualityChecks}>
          <View style={styles.qualityCheck}>
            {imageQuality.isGoodLighting ? 
              <CheckCircle size={14} color={Colors.light.success} /> :
              <AlertTriangle size={14} color={Colors.light.warning} />
            }
            <Text style={styles.qualityCheckText}>Iluminación</Text>
          </View>
          <View style={styles.qualityCheck}>
            {imageQuality.isInFocus ? 
              <CheckCircle size={14} color={Colors.light.success} /> :
              <AlertTriangle size={14} color={Colors.light.warning} />
            }
            <Text style={styles.qualityCheckText}>Enfoque</Text>
          </View>
          <View style={styles.qualityCheck}>
            {imageQuality.hasGoodResolution ? 
              <CheckCircle size={14} color={Colors.light.success} /> :
              <AlertTriangle size={14} color={Colors.light.warning} />
            }
            <Text style={styles.qualityCheckText}>Resolución</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderAIAnalysisResults = () => {
    if (!analysisResult) return null;

    return (
      <View style={styles.aiResultsContainer}>
        <View style={styles.aiResultsHeader}>
          <Zap size={20} color={Colors.light.accent} />
          <Text style={styles.aiResultsTitle}>Análisis IA Completado</Text>
          <View style={styles.confidenceBadge}>
            <Text style={styles.confidenceText}>{analysisResult.overallConfidence}% confianza</Text>
          </View>
        </View>
        
        <View style={styles.aiResultsSummary}>
          <Text style={styles.aiResultsSummaryText}>{analysisResult.overallCondition}</Text>
        </View>

        {analysisResult.recommendations && analysisResult.recommendations.length > 0 && (
          <View style={styles.recommendationsContainer}>
            <Text style={styles.recommendationsTitle}>Recomendaciones IA:</Text>
            {analysisResult.recommendations.slice(0, 3).map((rec, index) => (
              <Text key={index} style={styles.recommendationItem}>• {rec}</Text>
            ))}
          </View>
        )}
        
        <View style={styles.aiResultsNote}>
          <Text style={styles.aiResultsNoteText}>
            ✅ Todos los campos han sido pre-rellenados. Revisa cada zona antes de continuar.
          </Text>
        </View>
      </View>
    );
  };

  const renderDiagnosisStep = () => {
    // Original diagnosis interface
    const currentZone = diagnosisTab === "roots" ? HairZone.ROOTS : 
                       diagnosisTab === "mids" ? HairZone.MIDS : HairZone.ENDS;
    const progressData = calculateDiagnosisProgress();
    
    // Calculate gray percentage for the map
    const rootsGrayPercentage = zoneColorAnalysis[HairZone.ROOTS]?.grayPercentage || 0;

    return (
      <View style={styles.stepContainer}>
        <View style={styles.diagnosisHeader}>
          <View>
            <Text style={styles.stepTitle}>Diagnóstico Capilar Ultra-Inteligente</Text>
            {client && (
              <Text style={styles.clientName}>Cliente: {client?.name || 'Cliente'}</Text>
            )}
          </View>
        </View>
        

        {renderPrivacyBanner()}
        

        {/* Client History Panel */}
        {clientId && showHistoryPanel && (
          <ClientHistoryPanel 
            clientId={clientId as string}
            onRecommendationApply={handleRecommendationApply}
          />
        )}

        <View style={styles.tabsContainer}>
          <TouchableOpacity 
            style={[styles.tab, diagnosisMethod === "ai" && styles.activeTab]}
            onPress={() => setDiagnosisMethod("ai")}
          >
            <Zap size={16} color={diagnosisMethod === "ai" ? Colors.light.primary : Colors.light.gray} />
            <Text style={[styles.tabText, diagnosisMethod === "ai" && styles.activeTabText]}>
              Con IA ✨
            </Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.tab, diagnosisMethod === "manual" && styles.activeTab]}
            onPress={() => setDiagnosisMethod("manual")}
          >
            <Eye size={16} color={diagnosisMethod === "manual" ? Colors.light.primary : Colors.light.gray} />
            <Text style={[styles.tabText, diagnosisMethod === "manual" && styles.activeTabText]}>
              Manual
            </Text>
          </TouchableOpacity>
        </View>

        {/* Subtle help text when AI data is present */}
        {isDataFromAI && diagnosisMethod === "ai" && (
          <Text style={styles.helpText}>
            Los valores son editables • Toca para modificar
          </Text>
        )}

        <Text style={styles.sectionTitle}>Fotografías del cabello (3-5 ángulos)</Text>
        
        <PhotoGallery
          photos={hairPhotos}
          onAddPhoto={() => {
            if (supportsGuidedCamera) {
              // Determine next angle to capture
              const capturedAngles = hairPhotos.map(p => p.angle);
              const nextGuide = PHOTO_GUIDES.find(g => !capturedAngles.includes(g.angle));
              if (nextGuide) {
                setCurrentPhotoAngle(nextGuide.angle);
                setCameraMode('diagnosis');
                setShowGuidedCamera(true);
              }
            } else {
              // iOS: Use standard camera and add to hair photos
              takePhoto(async (uri) => {
                const quality = performImageQualityCheck(uri);
                const qualityObj: PhotoQuality = {
                  lighting: quality.isGoodLighting ? 'good' : 'fair',
                  focus: quality.isInFocus ? 'good' : 'fair',
                  stability: 'good',
                  overall: quality.overallScore
                };
                
                const capturedAngles = hairPhotos.map(p => p.angle);
                const nextGuide = PHOTO_GUIDES.find(g => !capturedAngles.includes(g.angle));
                const angle = nextGuide?.angle || PhotoAngle.CROWN;
                
                const newPhoto: CapturedPhoto = {
                  id: Date.now().toString(),
                  uri,
                  angle,
                  quality: qualityObj,
                  timestamp: new Date()
                };
                
                setHairPhotos(prev => [...prev, newPhoto]);
              });
            }
          }}
          onRemovePhoto={(photoId) => {
            setHairPhotos(prev => prev.filter(p => p.id !== photoId));
          }}
          onRetakePhoto={(photo) => {
            if (supportsGuidedCamera) {
              setCurrentPhotoAngle(photo.angle);
              setCameraMode('diagnosis');
              setShowGuidedCamera(true);
              setCameraActive(true); // v10: Diagnosis flow activates camera immediately
            }
            // Remove the old photo
            setHairPhotos(prev => prev.filter(p => p.id !== photo.id));
            if (!supportsGuidedCamera) {
              // iOS: Use standard camera after removing
              takePhoto(async (uri) => {
                const quality = performImageQualityCheck(uri);
                const qualityObj: PhotoQuality = {
                  lighting: quality.isGoodLighting ? 'good' : 'fair',
                  focus: quality.isInFocus ? 'good' : 'fair',
                  stability: 'good',
                  overall: quality.overallScore
                };
                
                const newPhoto: CapturedPhoto = {
                  id: Date.now().toString(),
                  uri,
                  angle: photo.angle, // Use same angle as retaken photo
                  quality: qualityObj,
                  timestamp: new Date()
                };
                
                setHairPhotos(prev => [...prev, newPhoto]);
              });
            }
          }}
          maxPhotos={5}
        />
        
        <View style={styles.photoActionsContainer}>
          <TouchableOpacity 
            style={styles.photoActionButton}
            onPress={() => {
              if (supportsGuidedCamera) {
                const capturedAngles = hairPhotos.map(p => p.angle);
                const nextGuide = PHOTO_GUIDES.find(g => !capturedAngles.includes(g.angle));
                if (nextGuide) {
                  setCurrentPhotoAngle(nextGuide.angle);
                  setCameraMode('diagnosis'); // v10: Set camera mode
                  setShowGuidedCamera(true);
                  setCameraActive(true); // v10: Diagnosis flow activates camera immediately
                }
              } else {
                // iOS: Use standard camera and add to hair photos
                takePhoto(async (uri) => {
                  const quality = performImageQualityCheck(uri);
                  const qualityObj: PhotoQuality = {
                    lighting: quality.isGoodLighting ? 'good' : 'fair',
                    focus: quality.isInFocus ? 'good' : 'fair',
                    stability: 'good',
                    overall: quality.overallScore
                  };
                  
                  const capturedAngles = hairPhotos.map(p => p.angle);
                  const nextGuide = PHOTO_GUIDES.find(g => !capturedAngles.includes(g.angle));
                  const angle = nextGuide?.angle || PhotoAngle.CROWN;
                  
                  const newPhoto: CapturedPhoto = {
                    id: Date.now().toString(),
                    uri,
                    angle,
                    quality: qualityObj,
                    timestamp: new Date()
                  };
                  
                  setHairPhotos(prev => [...prev, newPhoto]);
                });
              }
            }}
          >
            <Camera size={16} color="white" />
            <Text style={styles.photoActionText}>{supportsGuidedCamera ? "Captura Guiada" : "Tomar Foto"}</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.photoActionButton, styles.photoActionButtonSecondary]}
            onPress={() => pickMultipleImages()}
          >
            <Upload size={16} color={Colors.light.primary} />
            <Text style={[styles.photoActionText, styles.photoActionTextSecondary]}>
              Subir Fotos
            </Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.privacyNote}>
          <Shield size={14} color={Colors.light.success} />
          <Text style={styles.privacyNoteText}>
            Las imágenes se procesan localmente y se eliminan tras el análisis
          </Text>
        </View>

        {renderImageQualityIndicator()}

        {/* AI Analysis Button - Single button for complete analysis */}
        {diagnosisMethod === "ai" && hairPhotos.length > 0 && (
          <TouchableOpacity 
            style={[styles.aiButton, isAnalyzing && styles.aiButtonDisabled]}
            onPress={handleAIAnalysis}
            disabled={isAnalyzing}
          >
            {isAnalyzing ? (
              <View style={styles.aiButtonLoading}>
                <ActivityIndicator size="small" color="white" />
                <Text style={styles.aiButtonText}>Analizando {hairPhotos.length} fotos...</Text>
              </View>
            ) : (
              <View style={styles.aiButtonContent}>
                <Zap size={20} color="white" />
                <Text style={styles.aiButtonText}>
                  Analizar {hairPhotos.length} {hairPhotos.length === 1 ? 'Foto' : 'Fotos'} con IA
                </Text>
              </View>
            )}
          </TouchableOpacity>
        )}

        {renderAIAnalysisResults()}

        {/* General characteristics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Características Generales</Text>
          
          <DiagnosisSelector
            label="Grosor del cabello"
            value={hairThickness}
            options={getHairThicknessOptions()}
            onValueChange={(value) => setHairThickness(value)}
            required
            isFromAI={isDataFromAI}
          />

          <DiagnosisSelector
            label="Densidad del cabello"
            value={hairDensity}
            options={getHairDensityOptions()}
            onValueChange={(value) => setHairDensity(value)}
            required
            isFromAI={isDataFromAI}
          />

          <DiagnosisSelector
            label="Tono general predominante"
            value={overallTone}
            options={getNaturalToneOptions()}
            onValueChange={(value) => setOverallTone(value)}
            required
            isFromAI={isDataFromAI}
          />

          <DiagnosisSelector
            label="Subtono dominante"
            value={overallUndertone}
            options={getUndertoneOptions()}
            onValueChange={(value) => setOverallUndertone(value)}
            required
            isFromAI={isDataFromAI}
          />

          {/* Chemical process history */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Último proceso químico</Text>
            <TextInput
              style={styles.input}
              value={lastChemicalProcessType}
              onChangeText={setLastChemicalProcessType}
              placeholder="Ej: Decoloración, Tinte permanente, Alisado..."
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Fecha del último proceso</Text>
            <TextInput
              style={styles.input}
              value={lastChemicalProcessDate}
              onChangeText={setLastChemicalProcessDate}
              placeholder="Ej: 15/05/2024"
            />
          </View>

          {/* Remedios caseros */}
          <View style={styles.formGroup}>
            <View style={styles.switchContainer}>
              <Text style={styles.label}>¿Ha usado remedios caseros?</Text>
              <Switch
                value={homeRemedies}
                onValueChange={setHomeRemedies}
                trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
                thumbColor={homeRemedies ? Colors.light.surface : Colors.light.textSecondary}
              />
            </View>
            {homeRemedies && (
              <Text style={styles.helperText}>
                ⚠️ Limón, vinagre, manzanilla, etc. pueden afectar el resultado
              </Text>
            )}
          </View>
        </View>

        {/* Mediciones físicas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Mediciones Físicas</Text>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>
              Longitud total del cabello ({useSalonConfigStore.getState().configuration.measurementSystem === 'metric' ? 'cm' : 'inches'})
            </Text>
            <TextInput
              style={styles.input}
              value={totalHairLength}
              onChangeText={setTotalHairLength}
              placeholder={`0.0 ${useSalonConfigStore.getState().configuration.measurementSystem === 'metric' ? 'cm' : 'inches'}`}
              keyboardType="decimal-pad"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>
              Crecimiento mensual ({useSalonConfigStore.getState().configuration.measurementSystem === 'metric' ? 'cm' : 'inches'})
            </Text>
            <TextInput
              style={styles.input}
              value={monthlyGrowth}
              onChangeText={setMonthlyGrowth}
              placeholder={`1.25 ${useSalonConfigStore.getState().configuration.measurementSystem === 'metric' ? 'cm' : 'inches'}`}
              keyboardType="decimal-pad"
            />
          </View>
        </View>

        {/* Zone tabs */}
        <View style={styles.zoneTabs}>
          <TouchableOpacity 
            style={[styles.zoneTab, diagnosisTab === "roots" && styles.activeZoneTab]}
            onPress={() => setDiagnosisTab("roots")}
          >
            <Text style={[styles.zoneTabText, diagnosisTab === "roots" && styles.activeZoneTabText]}>
              Raíces
            </Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.zoneTab, diagnosisTab === "mids" && styles.activeZoneTab]}
            onPress={() => setDiagnosisTab("mids")}
          >
            <Text style={[styles.zoneTabText, diagnosisTab === "mids" && styles.activeZoneTabText]}>
              Medios
            </Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.zoneTab, diagnosisTab === "ends" && styles.activeZoneTab]}
            onPress={() => setDiagnosisTab("ends")}
          >
            <Text style={[styles.zoneTabText, diagnosisTab === "ends" && styles.activeZoneTabText]}>
              Puntas
            </Text>
          </TouchableOpacity>
        </View>


        {/* Zone-specific diagnosis */}
        <ZoneDiagnosisForm
          zone={currentZone}
          colorAnalysis={zoneColorAnalysis[currentZone]}
          physicalAnalysis={zonePhysicalAnalysis[currentZone]}
          onColorChange={(analysis) => {
            setZoneColorAnalysis(prev => ({
              ...prev,
              [currentZone]: { ...prev[currentZone], ...analysis }
            }));
          }}
          onPhysicalChange={(analysis) => {
            setZonePhysicalAnalysis(prev => ({
              ...prev,
              [currentZone]: { ...prev[currentZone], ...analysis }
            }));
          }}
          isFromAI={isDataFromAI}
        />


        <View style={styles.formGroup}>
          <Text style={styles.label}>Notas adicionales</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={diagnosisNotes}
            onChangeText={setDiagnosisNotes}
            placeholder="Observaciones adicionales sobre el cabello"
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
      </View>
    );
  };

  const handleDesiredPhotoAdd = async (isCamera: boolean, useGuidedCapture: boolean = false, photoType?: DesiredPhotoType) => {
    console.log('[handleDesiredPhotoAdd] Called with:', {
      isCamera,
      useGuidedCapture,
      photoType,
      desiredPhotosCount: desiredPhotos.length,
      platform: Platform.OS,
      currentStep,
      timestamp: new Date().toISOString()
    });
    
    try {
      if (isCamera) {
        // Check if guided capture is supported on this platform
        if (useGuidedCapture && supportsGuidedCamera) {
          // Check photo limit
          if (desiredPhotos.length >= 5) {
            Alert.alert('Límite alcanzado', 'Ya has capturado el máximo de fotos de referencia');
            return;
          }
          
          // Find the guide for the specific photo type
          let guide;
          if (photoType) {
            console.log('[handleDesiredPhotoAdd] Looking for guide with type:', photoType);
            guide = DESIRED_PHOTO_GUIDES.find(g => g.type === photoType);
          } else {
            // Fallback to sequential order if no type specified
            console.log('[handleDesiredPhotoAdd] No photoType, using sequential order');
            guide = DESIRED_PHOTO_GUIDES[desiredPhotos.length] || DESIRED_PHOTO_GUIDES[0];
          }
          
          if (!guide) {
            console.error('[handleDesiredPhotoAdd] No guide found for type:', photoType);
            Alert.alert('Error', 'No se pudo determinar el tipo de foto a capturar');
            return;
          }
          
          console.log('[handleDesiredPhotoAdd] Found guide:', {
            type: guide.type,
            label: guide.label,
            angle: guide.angle
          });
          
          const angle = guide.angle || PhotoAngle.FRONT;
          
          // Validate angle
          if (!Object.values(PhotoAngle).includes(angle)) {
            console.error('[handleDesiredPhotoAdd] Invalid angle:', angle);
            Alert.alert('Error', 'Configuración de cámara inválida');
            return;
          }
          
          // Store the desired photo type for later use
          currentDesiredPhotoTypeRef.current = guide.type;
          
          console.log('[handleDesiredPhotoAdd] Setting camera state:', {
            angle,
            cameraMode: 'desired',
            photoType: guide.type
          });
          
          // Set states in proper order
          setCurrentPhotoAngle(angle);
          setCameraMode('desired');
          
          // Open guided camera
          setShowGuidedCamera(true);
          setCameraActive(true);
          
          return;
        }
        
        // Regular camera (without guided capture or when guided is not supported)
        try {
          const { status } = await ImagePicker.requestCameraPermissionsAsync();
          if (status !== 'granted') {
            Alert.alert('Permisos necesarios', 'Se necesitan permisos de cámara para tomar fotos');
            return;
          }
          
          const result = await ImagePicker.launchCameraAsync({
            quality: 0.5,
            allowsEditing: false,
            base64: false,
          });
          
          if (!result.canceled && result.assets[0]) {
            // Determine photo type based on current count or requested type
            let type = photoType;
            if (!type) {
              if (desiredPhotos.length === 0) {
                type = DesiredPhotoType.MAIN;
              } else if (desiredPhotos.length === 1) {
                type = DesiredPhotoType.DETAILS;
              } else if (desiredPhotos.length === 2) {
                type = DesiredPhotoType.ALTERNATIVE;
              } else if (desiredPhotos.length === 3) {
                type = DesiredPhotoType.ROOTS_CONTRAST;
              } else {
                type = DesiredPhotoType.DIMENSION;
              }
            }
            
            const newPhoto: DesiredPhoto = {
              id: Date.now().toString(),
              uri: result.assets[0].uri,
              type,
              timestamp: new Date()
            };
            
            setDesiredPhotos(prev => [...prev, newPhoto]);
            setToastMessage("✅ Foto capturada correctamente");
            setShowToast(true);
          }
        } catch (error) {
          console.error('[DesiredPhoto] Error with ImagePicker:', error);
          Alert.alert('Error', 'No se pudo acceder a la cámara');
        }
      } else {
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsMultipleSelection: true,
          quality: 0.5, // Reducido para consistencia y evitar problemas de memoria
          selectionLimit: 5 - desiredPhotos.length,
          base64: false, // No cargar base64 para ahorrar memoria
        });
        if (!result.canceled && result.assets) {
          result.assets.forEach(asset => addDesiredPhoto(asset.uri));
        }
      }
    } catch (error) {
      console.error('Error al agregar foto deseada:', error);
      Alert.alert(
        'Error', 
        isCamera ? 
          'No se pudo acceder a la cámara. Por favor, usa la galería mientras resolvemos este problema.' :
          'No se pudo acceder a la galería.',
        isCamera ? [
          { text: 'Usar Galería', onPress: () => handleDesiredPhotoAdd(false) },
          { text: 'Cancelar' }
        ] : [
          { text: 'OK' }
        ]
      );
    }
  };

  const addDesiredPhoto = (uri: string) => {
    // Assign photo type based on current count following the new guides
    let photoType: DesiredPhotoType;
    if (desiredPhotos.length === 0) {
      photoType = DesiredPhotoType.MAIN;
    } else if (desiredPhotos.length === 1) {
      photoType = DesiredPhotoType.DETAILS;
    } else if (desiredPhotos.length === 2) {
      photoType = DesiredPhotoType.ALTERNATIVE;
    } else if (desiredPhotos.length === 3) {
      photoType = DesiredPhotoType.ROOTS_CONTRAST;
    } else {
      photoType = DesiredPhotoType.DIMENSION;
    }
    
    const newPhoto: DesiredPhoto = {
      id: Date.now().toString(),
      uri,
      type: photoType,
      timestamp: new Date()
    };
    setDesiredPhotos(prev => [...prev, newPhoto]);
  };

  const analyzeDesiredPhotos = async () => {
    if (desiredPhotos.length === 0) {
      Alert.alert("Error", "Por favor agrega al menos una foto de referencia");
      return;
    }

    setIsAnalyzingDesired(true);
    
    // Simulate AI analysis of desired photos
    setTimeout(() => {
      // Create comprehensive analysis result
      const analysisResult: DesiredColorAnalysisResult = {
        general: {
          overallLevel: "8/9",
          overallTone: "Rubio ceniza con reflejos rosados",
          technique: "balayage",
          customTechnique: ""
        },
        zones: {
          [HairZone.ROOTS]: {
            zone: HairZone.ROOTS,
            desiredLevel: 7,
            desiredTone: "Rubio oscuro",
            desiredUndertone: "Ceniza",
            coverage: 100
          },
          [HairZone.MIDS]: {
            zone: HairZone.MIDS,
            desiredLevel: 8,
            desiredTone: "Rubio medio",
            desiredUndertone: "Beige",
            coverage: 80
          },
          [HairZone.ENDS]: {
            zone: HairZone.ENDS,
            desiredLevel: 9,
            desiredTone: "Rubio claro",
            desiredUndertone: "Platino",
            coverage: 100
          }
        },
        advanced: {
          contrast: 'medium',
          direction: 'cooler',
          graysCoverage: 100,
          finalTexture: 'glossy',
          specialNotes: ""
        },
        lifestyle: {
          maintenanceLevel: MaintenanceLevel.MEDIUM, // Default to medium maintenance
          avoidTones: [],
          budgetLevel: BudgetLevel.STANDARD
        },
        confidence: 85,
        isFromAI: true
      };
      
      setDesiredAnalysisResult(analysisResult);
      
      // Automatically analyze viability after desired analysis
      const viability = analyzeViability();
      setViabilityAnalysis(viability);
      
      setToastMessage("✅ Referencias analizadas. Revisa y ajusta según tu criterio");
      setShowToast(true);
      setIsAnalyzingDesired(false);
    }, 2000);
  };

  // Removed handleDesiredCameraCapture - now using navigation to desired-camera screen

  const renderDesiredResultStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Resultado Deseado</Text>
      {client && (
        <Text style={styles.clientName}>Cliente: {client?.name || 'Cliente'}</Text>
      )}

      {/* Method selector tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity 
          style={[styles.tab, desiredMethod === "ai" && styles.activeTab]}
          onPress={() => setDesiredMethod("ai")}
        >
          <Zap size={16} color={desiredMethod === "ai" ? Colors.light.primary : Colors.light.gray} />
          <Text style={[styles.tabText, desiredMethod === "ai" && styles.activeTabText]}>
            Con IA ✨
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, desiredMethod === "manual" && styles.activeTab]}
          onPress={() => setDesiredMethod("manual")}
        >
          <Eye size={16} color={desiredMethod === "manual" ? Colors.light.primary : Colors.light.gray} />
          <Text style={[styles.tabText, desiredMethod === "manual" && styles.activeTabText]}>
            Manual
          </Text>
        </TouchableOpacity>
      </View>

      {desiredMethod === "ai" ? (
        <>
          <Text style={styles.sectionTitle}>Referencias del color deseado (3-5 fotos)</Text>
          
          <DesiredPhotoGallery
            photos={desiredPhotos}
            onAddPhoto={handleDesiredPhotoAdd}
            onRemovePhoto={(photoId) => {
              setDesiredPhotos(prev => prev.filter(p => p.id !== photoId));
            }}
            maxPhotos={5}
          />

          {/* AI Analysis Button for desired photos */}
            {desiredPhotos.length > 0 && !desiredAnalysisResult && (
              <TouchableOpacity 
                style={[styles.aiButton, isAnalyzingDesired && styles.aiButtonDisabled]}
                onPress={analyzeDesiredPhotos}
                disabled={isAnalyzingDesired}
              >
                {isAnalyzingDesired ? (
                  <View style={styles.aiButtonLoading}>
                    <ActivityIndicator size="small" color="white" />
                    <Text style={styles.aiButtonText}>Analizando referencias...</Text>
                  </View>
                ) : (
                  <View style={styles.aiButtonContent}>
                    <Zap size={20} color="white" />
                    <Text style={styles.aiButtonText}>
                      Analizar {desiredPhotos.length} {desiredPhotos.length === 1 ? 'Referencia' : 'Referencias'}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            )}
          </>
        ) : (
          // Manual mode - show form directly
          <Text style={styles.helpText}>
            Ingresa manualmente los datos del color deseado
          </Text>
        )}

      {/* Desired Color Analysis Form - Always visible */}
      <DesiredColorAnalysisForm
        analysisResult={desiredAnalysisResult}
        onAnalysisChange={setDesiredAnalysisResult}
        isFromAI={desiredMethod === "ai" && (desiredAnalysisResult?.isFromAI || false)}
      />
      
      {/* Viability Analysis - Show after desired analysis */}
      {desiredAnalysisResult && viabilityAnalysis && (
        <ViabilityIndicator analysis={viabilityAnalysis} />
      )}

      <View style={styles.formGroup}>
        <Text style={styles.label}>Notas adicionales</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={desiredNotes}
          onChangeText={setDesiredNotes}
          placeholder="Detalles adicionales sobre el resultado deseado"
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
      </View>
    </View>
  );

  const renderFormulationStep = () => {
    return (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Formulación Ultra-Inteligente</Text>
      {client && (
        <Text style={styles.clientName}>Cliente: {client?.name || 'Cliente'}</Text>
      )}

      <View style={styles.formGroup}>
        <Text style={styles.label}>Marca</Text>
        <TouchableOpacity 
          style={styles.selectContainer}
          onPress={() => {
            setBrandModalType('main');
            setShowBrandModal(true);
          }}
        >
          <Text style={styles.selectText}>{selectedBrand}</Text>
          <ChevronRight size={16} color={Colors.light.gray} />
        </TouchableOpacity>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Línea</Text>
        <TouchableOpacity 
          style={styles.selectContainer}
          onPress={() => {
            setBrandModalType('main');
            setShowBrandModal(true);
          }}
        >
          <Text style={styles.selectText}>{selectedLine}</Text>
          <ChevronRight size={16} color={Colors.light.gray} />
        </TouchableOpacity>
      </View>
      
      {/* Brand Conversion Section */}
      <View style={[styles.conversionSection, conversionMode && styles.conversionSectionActive]}>
        <TouchableOpacity 
          style={styles.conversionHeader}
          activeOpacity={0.7}
          onPress={() => setConversionMode(!conversionMode)}
        >
          <View style={styles.conversionTitleContainer}>
            <Text style={styles.conversionTitle}>Adaptar fórmula de otra marca</Text>
            <Text style={styles.conversionSubtitle}>Obtén el equivalente en tu marca</Text>
          </View>
          <Switch
            trackColor={{ false: Colors.light.lightGray, true: Colors.light.primary }}
            thumbColor={conversionMode ? Colors.light.primary : Colors.light.gray}
            ios_backgroundColor={Colors.light.lightGray}
            onValueChange={setConversionMode}
            value={conversionMode}
            style={[styles.conversionSwitch, { opacity: 1 }]}
          />
        </TouchableOpacity>
        
        {conversionMode && (
          <View style={styles.conversionContent}>

            {/* Formula Input First */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>¿Cuál es la fórmula?</Text>
              <TextInput
                style={[styles.input, styles.textArea, styles.conversionFormulaInput]}
                value={originalFormula}
                onChangeText={setOriginalFormula}
                placeholder="Ej: 7.31 + 8.34 (2:1) • Oxidante 20 vol • 35 min"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>

            <TouchableOpacity 
              style={styles.conversionBrandContainer}
              onPress={() => {
                setBrandModalType('conversion');
                setShowBrandModal(true);
              }}
            >
              <Text style={styles.conversionLabel}>¿De qué marca es la fórmula?</Text>
              <View style={styles.conversionSelectContainer}>
                <Text style={styles.selectText}>
                  {originalBrand || "Seleccionar marca..."}
                </Text>
                <ChevronRight size={16} color={Colors.light.gray} />
              </View>
            </TouchableOpacity>
            
            {originalBrand && (
              <TouchableOpacity 
                style={styles.conversionBrandContainer}
                onPress={() => {
                  setBrandModalType('conversion');
                  setShowBrandModal(true);
                }}
              >
                <Text style={styles.conversionLabel}>¿Qué línea o producto?</Text>
                <View style={styles.conversionSelectContainer}>
                  <Text style={styles.selectText}>
                    {originalLine || "Seleccionar línea..."}
                  </Text>
                  <ChevronRight size={16} color={Colors.light.gray} />
                </View>
              </TouchableOpacity>
            )}
            
            {originalBrand && originalLine && (
              <View style={styles.conversionInfoBox}>
                <Text style={styles.conversionInfoText}>
                  ✨ Se adaptará a {selectedBrand} {selectedLine}
                </Text>
                <Text style={styles.conversionInfoSubtext}>
                  Manteniendo el mismo resultado de color
                </Text>
              </View>
            )}
          </View>
        )}
      </View>

      <TouchableOpacity 
        style={[styles.aiButton, isGeneratingFormula && styles.aiButtonDisabled]}
        onPress={generateFormulaWithAI}
        disabled={isGeneratingFormula}
      >
        {isGeneratingFormula ? (
          <View style={styles.aiButtonLoading}>
            <ActivityIndicator size="small" color="white" />
            <Text style={styles.aiButtonText}>Generando fórmula...</Text>
          </View>
        ) : (
          <View style={styles.aiButtonContent}>
            <Zap size={20} color="white" />
            <Text style={styles.aiButtonText}>{conversionMode && originalBrand && originalLine ? 'Convertir y Generar Fórmula' : 'Generar Fórmula Ultra-Inteligente'}</Text>
          </View>
        )}
      </TouchableOpacity>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Fórmula</Text>
        {formula ? (
          <FormulaVisualization
            formulaText={formula}
            onFormulaChange={setFormula}
            editable={true}
            context={{
              clientName: client?.name,
              serviceDate: new Date().toLocaleDateString(),
              currentLevel: analysisResult?.averageDepthLevel,
              targetLevel: desiredAnalysisResult?.general?.overallLevel ? parseFloat(desiredAnalysisResult.general.overallLevel) : undefined,
              currentTone: analysisResult?.overallTone,
              targetTone: desiredAnalysisResult?.general?.overallTone,
              levelDifference: viabilityAnalysis?.levelDifference,
              correctionNeeded: viabilityAnalysis?.correctionNeeded
            }}
          />
        ) : (
          <TextInput
            style={[styles.input, styles.textArea, styles.formulaTextArea]}
            value={formula}
            onChangeText={setFormula}
            placeholder="Detalle de la fórmula, proporciones, tiempos de pose, etc."
            multiline
            numberOfLines={8}
            textAlignVertical="top"
          />
        )}
      </View>

      {/* Viability Analysis */}
      {viabilityAnalysis && (
        <ViabilityIndicator analysis={viabilityAnalysis} />
      )}

      {/* Formula Cost Breakdown */}
      {formulaCost && (
        <FormulaCostBreakdown 
          cost={formulaCost} 
          isRealCost={useSalonConfigStore.getState().configuration.inventoryControlLevel !== 'solo-formulas'}
        />
      )}
      
      {/* Stock Validation */}
      {formula && useSalonConfigStore.getState().configuration.inventoryControlLevel === 'control-total' && (
        <View style={styles.stockValidationSection}>
          <TouchableOpacity 
            style={[styles.stockCheckButton, stockValidation.isChecking && styles.stockCheckButtonDisabled]}
            onPress={checkStockAvailability}
            disabled={stockValidation.isChecking}
          >
            {stockValidation.isChecking ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <>
                <Package size={20} color="white" />
                <Text style={styles.stockCheckButtonText}>
                  {stockValidation.checked ? 'Verificar Stock Nuevamente' : 'Verificar Disponibilidad'}
                </Text>
              </>
            )}
          </TouchableOpacity>
          
          {stockValidation.checked && (
            <View style={[
              styles.stockStatus, 
              stockValidation.hasStock ? styles.stockStatusSuccess : styles.stockStatusError
            ]}>
              {stockValidation.hasStock ? (
                <>
                  <CheckCircle size={20} color={Colors.light.success} />
                  <Text style={styles.stockStatusTextSuccess}>Stock disponible para todos los productos</Text>
                </>
              ) : (
                <>
                  <AlertTriangle size={20} color={Colors.light.error} />
                  <View style={styles.stockStatusContent}>
                    <Text style={styles.stockStatusTextError}>Stock insuficiente:</Text>
                    {stockValidation.missingProducts.map((product, index) => (
                      <Text key={index} style={styles.missingProductText}>• {product}</Text>
                    ))}
                  </View>
                </>
              )}
            </View>
          )}
        </View>
      )}
    </View>
    );
  };

  const renderFinalResultStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Resultado Final</Text>
      {client && (
        <Text style={styles.clientName}>Cliente: {client?.name || 'Cliente'}</Text>
      )}

      <Text style={styles.sectionTitle}>Fotos del resultado final</Text>
      <View style={styles.photoContainer}>
        {resultImage ? (
          <Image source={{ uri: resultImage }} style={styles.photoPreview} />
        ) : (
          <View style={styles.photoPlaceholder}>
            <Camera size={40} color={Colors.light.gray} />
            <Text style={styles.photoPlaceholderText}>Fotografía del resultado</Text>
          </View>
        )}
        <View style={styles.photoButtons}>
          <TouchableOpacity 
            style={styles.photoButton}
            onPress={() => takePhoto(setResultImage)}
          >
            <Camera size={16} color="white" />
            <Text style={styles.photoButtonText}>Usar Cámara</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.photoButton}
            onPress={() => pickImage(setResultImage)}
          >
            <Upload size={16} color="white" />
            <Text style={styles.photoButtonText}>Seleccionar</Text>
          </TouchableOpacity>
        </View>
        <Text style={styles.photoTip}>
          🔒 PRIVACIDAD: Las imágenes se procesan con difuminado facial automático y se eliminan inmediatamente después del análisis.
        </Text>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Satisfacción del cliente (1-5)</Text>
        <View style={styles.satisfactionContainer}>
          {[1, 2, 3, 4, 5].map((rating) => (
            <TouchableOpacity
              key={rating}
              style={[
                styles.satisfactionButton,
                clientSatisfaction === rating && styles.satisfactionButtonActive
              ]}
              onPress={() => setClientSatisfaction(rating)}
            >
              <Text style={[
                styles.satisfactionButtonText,
                clientSatisfaction === rating && styles.satisfactionButtonTextActive
              ]}>
                {rating}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Notas finales</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={resultNotes}
          onChangeText={setResultNotes}
          placeholder="Observaciones sobre el resultado, ajustes realizados, recomendaciones para el cliente, etc."
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
      </View>
      
      {/* Inventory Consumption Section */}
      {formula && useSalonConfigStore.getState().configuration.inventoryControlLevel === 'control-total' && (
        <View style={styles.inventoryConsumptionSection}>
          <View style={styles.inventoryConsumptionHeader}>
            <Text style={styles.inventoryConsumptionTitle}>Control de Inventario</Text>
            <Switch
              trackColor={{ false: Colors.light.lightGray, true: Colors.light.primary }}
              thumbColor={"white"}
              ios_backgroundColor={Colors.light.lightGray}
              onValueChange={setConsumeInventory}
              value={consumeInventory}
            />
          </View>
          
          {consumeInventory && formulaCost && (
            <View style={styles.inventoryConsumptionDetails}>
              <Text style={styles.inventoryConsumptionSubtitle}>Productos a descontar:</Text>
              {formulaCost.items.map((item, index) => (
                <View key={index} style={styles.inventoryItem}>
                  <Text style={styles.inventoryItemName}>{item.product}</Text>
                  <Text style={styles.inventoryItemAmount}>{item.amount}</Text>
                </View>
              ))}
              
              <View style={styles.inventoryWarning}>
                <AlertTriangle size={16} color={Colors.light.warning} />
                <Text style={styles.inventoryWarningText}>
                  Esta acción descontará estos productos de tu inventario
                </Text>
              </View>
            </View>
          )}
        </View>
      )}
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderDiagnosisStep();
      case 1:
        return renderDesiredResultStep();
      case 2:
        return renderFormulationStep();
      case 3:
        return renderFinalResultStep();
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{STEPS[currentStep].title}</Text>
        <View style={styles.placeholder} />
      </View>

      {renderProgressBar()}

      {showToast && (
        <Toast 
          message={toastMessage} 
          onHide={() => setShowToast(false)} 
        />
      )}

      <ScrollView 
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {renderCurrentStep()}
      </ScrollView>
      
      {/* Guided Camera Modal - For both diagnosis and desired */}
      {/* Moved outside ScrollView for iOS stability */}
      {(cameraMode === 'diagnosis' || cameraMode === 'desired') && (
        <GuidedCamera
          visible={showGuidedCamera}
          active={cameraActive}
          currentAngle={currentPhotoAngle}
          mode={cameraMode}
          onCapture={cameraMode === 'desired' ? handleDesiredCameraCapture : handleCameraCapture}
          onClose={() => {
            console.log(`[GuidedCamera] Closing ${cameraMode} camera`);
            setShowGuidedCamera(false);
            setCameraActive(false);
            setCameraMode(null);
          }}
          onSkip={() => {
            if (cameraMode === 'diagnosis') {
              // Find next angle for diagnosis photos
              const capturedAngles = hairPhotos.map(p => p.angle);
              const nextGuide = PHOTO_GUIDES.find(g => 
                g.angle !== currentPhotoAngle && !capturedAngles.includes(g.angle)
              );
              if (nextGuide) {
                setCurrentPhotoAngle(nextGuide.angle);
              } else {
                setShowGuidedCamera(false);
                setCameraActive(false);
                setCameraMode(null);
              }
            } else {
              // For desired mode, just close
              setShowGuidedCamera(false);
              setCameraActive(false);
              setCameraMode(null);
            }
          }}
        />
      )}
        
        {/* Brand Selection Modal */}
        <BrandSelectionModal
          visible={showBrandModal}
          onClose={() => setShowBrandModal(false)}
          onSelectBrand={(brand, line) => {
            if (brandModalType === 'main') {
              setSelectedBrand(brand);
              setSelectedLine(line);
              // Si está en modo conversión y la marca de destino es igual a la de origen, limpiar origen
              if (conversionMode && brand === originalBrand) {
                setOriginalBrand("");
                setOriginalLine("");
                Alert.alert("Atención", "La marca de destino no puede ser igual a la marca de origen");
              }
            } else {
              // Si la marca de origen es igual a la de destino, no permitir
              if (brand === selectedBrand) {
                Alert.alert("Atención", "La marca de origen debe ser diferente a tu marca destino");
                return;
              }
              setOriginalBrand(brand);
              setOriginalLine(line);
            }
            setShowBrandModal(false);
          }}
          currentBrand={brandModalType === 'main' ? selectedBrand : originalBrand}
          currentLine={brandModalType === 'main' ? selectedLine : originalLine}
          title={brandModalType === 'main' ? "Seleccionar Marca y Línea" : "Marca y Línea Original"}
          isConversionMode={brandModalType === 'conversion'}
          sourceBrand={brandModalType === 'conversion' ? originalBrand : undefined}
          sourceLine={brandModalType === 'conversion' ? originalLine : undefined}
        />

        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={styles.backStepButton} 
            onPress={prevStep}
          >
            <Text style={styles.backStepButtonText}>
              {currentStep === 0 ? "Cancelar" : "Anterior"}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.nextStepButton, isConsumingInventory && styles.nextStepButtonDisabled]} 
            onPress={nextStep}
            disabled={isConsumingInventory}
          >
            {isConsumingInventory ? (
              <View style={styles.buttonLoadingContent}>
                <ActivityIndicator size="small" color="white" />
                <Text style={[styles.nextStepButtonText, { marginLeft: 8 }]}>
                  Guardando servicio...
                </Text>
              </View>
            ) : (
              <Text style={styles.nextStepButtonText}>
                {currentStep === 0 ? "Confirmar diagnóstico" : 
                 currentStep === STEPS.length - 1 ? "Finalizar" : "Siguiente"}
              </Text>
            )}
          </TouchableOpacity>
        </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F5F7",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  placeholder: {
    width: 34,
  },
  progressContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 15,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  progressStep: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.light.lightGray,
    justifyContent: "center",
    alignItems: "center",
  },
  progressStepActive: {
    backgroundColor: Colors.light.primary,
  },
  progressStepText: {
    color: "white",
    fontWeight: "bold",
  },
  progressLine: {
    height: 2,
    width: 30,
    backgroundColor: Colors.light.lightGray,
  },
  progressLineActive: {
    backgroundColor: Colors.light.primary,
  },
  scrollContainer: {
    flex: 1,
  },
  stepContainer: {
    padding: 15,
  },
  diagnosisHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  wizardToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  wizardToggleText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 5,
  },
  clientName: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 15,
  },
  autoSaveIndicator: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.success + "10",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    alignSelf: "flex-start",
    marginBottom: 16,
    gap: 6,
  },
  autoSaveText: {
    fontSize: 12,
    color: Colors.light.success,
    fontWeight: "500",
  },
  privacyBanner: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.success + "15",
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.success,
  },
  privacyBannerText: {
    fontSize: 12,
    color: Colors.light.success,
    marginLeft: 8,
    flex: 1,
    fontWeight: "500",
  },
  tabsContainer: {
    flexDirection: "row",
    backgroundColor: "white",
    borderRadius: 8,
    marginBottom: 20,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.light.primary,
  },
  tabText: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  activeTabText: {
    color: Colors.light.primary,
    fontWeight: "600",
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
  },
  photoContainer: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
  },
  imageContainer: {
    position: "relative",
  },
  photoPreview: {
    width: "100%",
    height: 200,
    borderRadius: 8,
    marginBottom: 15,
  },
  processingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 15,
    backgroundColor: "rgba(0,0,0,0.7)",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  processingText: {
    color: "white",
    marginTop: 10,
    fontWeight: "500",
  },
  photoPlaceholder: {
    width: "100%",
    height: 200,
    borderRadius: 8,
    backgroundColor: "#F0F0F5",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 15,
  },
  photoPlaceholderText: {
    marginTop: 10,
    color: Colors.light.gray,
  },
  photoButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  photoButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 15,
    flex: 1,
    marginHorizontal: 5,
    justifyContent: "center",
  },
  photoButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 5,
  },
  photoTip: {
    fontSize: 12,
    color: Colors.light.success,
    marginTop: 10,
    textAlign: "center",
    fontWeight: "500",
  },
  qualityIndicator: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  qualityHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  qualityTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.text,
  },
  qualityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  qualityBadgeText: {
    fontSize: 12,
    fontWeight: "600",
  },
  qualityChecks: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  qualityCheck: {
    alignItems: "center",
    gap: 4,
  },
  qualityCheckText: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  aiResultsContainer: {
    backgroundColor: Colors.light.accent + "10",
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.accent + "30",
  },
  aiResultsHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  aiResultsTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: Colors.light.text,
    marginLeft: 8,
    flex: 1,
  },
  confidenceBadge: {
    backgroundColor: Colors.light.accent,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: "600",
    color: "white",
  },
  aiResultsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
    marginBottom: 16,
  },
  aiResultsSummary: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  aiResultsSummaryText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  aiResultsNote: {
    backgroundColor: Colors.light.success + "10",
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  aiResultsNoteText: {
    fontSize: 13,
    color: Colors.light.success,
    fontWeight: "500",
  },
  aiResultItem: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 12,
    flex: 1,
    minWidth: "45%",
  },
  aiResultLabel: {
    fontSize: 12,
    color: Colors.light.gray,
    marginBottom: 4,
  },
  aiResultValue: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.text,
  },
  recommendationsContainer: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 12,
  },
  recommendationsTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 8,
  },
  recommendationItem: {
    fontSize: 12,
    color: Colors.light.gray,
    lineHeight: 18,
    marginBottom: 4,
  },
  section: {
    marginBottom: 20,
  },
  zoneTabs: {
    flexDirection: "row",
    backgroundColor: "white",
    borderRadius: 8,
    marginBottom: 20,
  },
  zoneTab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: "center",
  },
  activeZoneTab: {
    backgroundColor: "rgba(106, 61, 232, 0.1)",
  },
  zoneTabText: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  activeZoneTabText: {
    color: Colors.light.primary,
    fontWeight: "600",
  },
  aiButton: {
    backgroundColor: Colors.light.accent,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginBottom: 20,
    shadowColor: Colors.light.accent,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  aiButtonDisabled: {
    backgroundColor: Colors.light.gray,
    shadowOpacity: 0,
    elevation: 0,
  },
  aiButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  aiButtonLoading: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  aiButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 15,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 12,
    textAlignVertical: "top",
  },
  formulaTextArea: {
    minHeight: 150,
    fontFamily: Platform.OS === "ios" ? "Menlo" : "monospace",
  },
  selectContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  selectText: {
    fontSize: 15,
  },
  satisfactionContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  satisfactionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: Colors.light.border,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "white",
  },
  satisfactionButtonActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  satisfactionButtonText: {
    fontSize: 18,
    fontWeight: "bold",
  },
  satisfactionButtonTextActive: {
    color: "white",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
    marginBottom: 30,
    paddingHorizontal: 15,
  },
  backStepButton: {
    flex: 1,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: "center",
    marginRight: 10,
  },
  backStepButtonText: {
    color: Colors.light.text,
    fontWeight: "600",
  },
  nextStepButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: "center",
  },
  nextStepButtonText: {
    color: "white",
    fontWeight: "600",
  },
  buttonLoadingContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  helpText: {
    fontSize: 12,
    color: Colors.light.gray,
    textAlign: "center",
    marginTop: -10,
    marginBottom: 15,
    fontStyle: "italic",
  },
  photoActionsContainer: {
    flexDirection: "row",
    gap: 12,
    marginTop: 16,
    marginBottom: 12,
  },
  photoActionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 12,
    gap: 8,
  },
  photoActionButtonSecondary: {
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  photoActionText: {
    color: "white",
    fontWeight: "600",
    fontSize: 14,
  },
  photoActionTextSecondary: {
    color: Colors.light.primary,
  },
  privacyNote: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.success + "15",
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  privacyNoteText: {
    fontSize: 12,
    color: Colors.light.success,
    flex: 1,
  },
  techniqueScroll: {
    marginTop: 8,
  },
  techniqueOption: {
    alignItems: "center",
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 12,
    marginRight: 12,
    minWidth: 80,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  techniqueOptionActive: {
    backgroundColor: Colors.light.primary + "10",
    borderColor: Colors.light.primary,
  },
  techniqueIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  techniqueName: {
    fontSize: 12,
    fontWeight: "500",
    color: Colors.light.gray,
    textAlign: "center",
  },
  techniqueNameActive: {
    color: Colors.light.primary,
    fontWeight: "600",
  },
  changeLevelContainer: {
    flexDirection: "row",
    gap: 8,
    marginTop: 8,
  },
  changeLevelOption: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 16,
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: "center",
  },
  changeLevelOptionActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  changeLevelText: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.light.text,
  },
  changeLevelTextActive: {
    color: "white",
    fontWeight: "600",
  },
  stockValidationSection: {
    backgroundColor: Colors.light.primary + "05",
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.primary + "20",
  },
  stockCheckButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    gap: 8,
  },
  stockCheckButtonDisabled: {
    backgroundColor: Colors.light.gray,
    opacity: 0.6,
  },
  stockCheckButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 15,
  },
  stockStatus: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  stockStatusSuccess: {
    backgroundColor: Colors.light.success + "10",
    borderWidth: 1,
    borderColor: Colors.light.success + "30",
  },
  stockStatusError: {
    backgroundColor: Colors.light.error + "10",
    borderWidth: 1,
    borderColor: Colors.light.error + "30",
  },
  stockStatusTextSuccess: {
    color: Colors.light.success,
    fontSize: 14,
    fontWeight: "500",
    flex: 1,
  },
  stockStatusTextError: {
    color: Colors.light.error,
    fontSize: 14,
    fontWeight: "500",
    flex: 1,
  },
  stockStatusContent: {
    flex: 1,
  },
  missingProductText: {
    fontSize: 12,
    color: Colors.light.error,
    marginTop: 4,
    marginLeft: 24,
  },
  inventoryConsumptionSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  inventoryConsumptionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  inventoryConsumptionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
  },
  inventoryConsumptionDetails: {
    marginTop: 12,
  },
  inventoryConsumptionSubtitle: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 12,
  },
  inventoryItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border + "30",
  },
  inventoryItemName: {
    fontSize: 14,
    color: Colors.light.text,
    flex: 1,
  },
  inventoryItemAmount: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.light.gray,
  },
  inventoryWarning: {
    backgroundColor: Colors.light.warning + "10",
    borderRadius: 8,
    padding: 12,
    marginTop: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  inventoryWarningText: {
    fontSize: 12,
    color: Colors.light.warning,
    flex: 1,
  },
  nextStepButtonDisabled: {
    backgroundColor: Colors.light.gray,
    opacity: 0.6,
  },
  conversionSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  conversionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
    minHeight: 60,
  },
  conversionTitleContainer: {
    flex: 1,
    marginRight: 12,
  },
  conversionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
  },
  conversionSubtitle: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 2,
  },
  conversionContent: {
    marginTop: 8,
  },
  conversionBrandContainer: {
    marginBottom: 16,
  },
  conversionLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.light.text,
    marginBottom: 8,
  },
  conversionSelectContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  conversionSwitch: {
    transform: Platform.OS === 'ios' ? [{ scale: 0.8 }] : [],
  },
  conversionInfoBox: {
    backgroundColor: Colors.light.primary + "10",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.primary + "20",
  },
  conversionInfoText: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.primary,
    marginBottom: 4,
  },
  conversionInfoSubtext: {
    fontSize: 13,
    color: Colors.light.text,
    lineHeight: 18,
  },
  conversionFormulaInput: {
    minHeight: 140,
    paddingTop: 12,
  },
  conversionNote: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 8,
    lineHeight: 16,
  },
  conversionSectionActive: {
    borderColor: Colors.light.primary,
    borderWidth: 2,
  },
  conversionReasonContainer: {
    marginBottom: 16,
  },
  conversionReasonTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 12,
  },
  conversionReasonOptions: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  conversionReasonChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: "white",
    marginBottom: 8,
  },
  conversionReasonChipActive: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primary + "10",
  },
  conversionReasonText: {
    fontSize: 13,
    color: Colors.light.text,
  },
  conversionReasonTextActive: {
    color: Colors.light.primary,
    fontWeight: "500",
  },
  switchContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  helperText: {
    fontSize: 13,
    color: Colors.light.warning,
    marginTop: 8,
    fontStyle: "italic",
  },
});