// Enums para valores estandarizados del diagnóstico capilar

export enum HairThickness {
  FINE = 'Fino',
  MEDIUM = 'Medio',
  THICK = 'Grueso'
}

export enum HairPorosity {
  LOW = 'Baja',
  MEDIUM = 'Media',
  HIGH = 'Alta'
}

export enum HairDensity {
  LOW = 'Baja',
  MEDIUM = 'Media',
  HIGH = 'Alta'
}

export enum HairElasticity {
  POOR = 'Pobre',
  MEDIUM = 'Media',
  GOOD = 'Buena'
}

export enum HairResistance {
  LOW = 'Baja',
  MEDIUM = 'Media',
  HIGH = 'Alta'
}

// Nuevos enums para colorimetría
export enum NaturalTone {
  BLACK = 'Negro',
  DARK_BROWN = 'Castaño Oscuro',
  MEDIUM_BROWN = 'Castaño Medio',
  LIGHT_BROWN = 'Castaño Claro',
  DARK_BLONDE = 'Rubio Oscuro',
  MEDIUM_BLONDE = 'Rubio Medio',
  LIGHT_BLONDE = 'Rubio Claro',
  VERY_LIGHT_BLONDE = 'Rubio Muy Claro',
  PLATINUM_BLONDE = 'Rubio Platino',
  RED = 'Rojo',
  AUBURN = 'Caoba',
  COPPER_RED = 'Rojo Cobrizo'
}

export enum Undertone {
  ASH = 'Cenizo',
  NATURAL = 'Natural',
  GOLDEN = 'Dorado',
  COPPER = 'Cobrizo',
  REDDISH = 'Rojizo',
  VIOLET = 'Violeta',
  MAHOGANY = 'Caoba',
  BEIGE = 'Beige',
  IRIDESCENT = 'Irisado'
}

// Estado del cabello
export enum HairState {
  NATURAL = 'Natural',
  COLORED = 'Teñido',
  BLEACHED = 'Decolorado',
  HIGHLIGHTED = 'Con mechas',
  PERMED = 'Permanente',
  STRAIGHTENED = 'Alisado',
  MIXED = 'Mixto'
}

// Zona del cabello
export enum HairZone {
  ROOTS = 'Raíces',
  MIDS = 'Medios',
  ENDS = 'Puntas'
}

// Tipo de análisis
export enum AnalysisType {
  AI = 'AI',
  MANUAL = 'Manual'
}

// Enum para matices no deseados
export enum UnwantedTone {
  ORANGE = 'Naranja',
  YELLOW = 'Amarillo',
  GREEN = 'Verde',
  RED = 'Rojo',
  ASHY_EXCESS = 'Cenizo excesivo'
}

// Enum para tipos de canas
export enum GrayHairType {
  RESISTANT = 'Resistente/Vidriosa',
  NORMAL = 'Normal',
  POROUS = 'Fina/Porosa'
}

// Enum para patrones de canas
export enum GrayPattern {
  SALT_PEPPER = 'Sal y pimienta',
  STREAKS = 'Mechas',
  PATCHES = 'Placas'
}

// Enum para estado de cutícula
export enum CuticleState {
  SMOOTH = 'Lisa',
  ROUGH = 'Áspera',
  DAMAGED = 'Dañada'
}

// Enum para riesgos detectados
export enum HairRisk {
  METAL_SALTS = 'Sales metálicas',
  HENNA = 'Henna',
  EXTREME_DAMAGE = 'Daño extremo',
  FORMALDEHYDE = 'Formol/Alisado',
  INCOMPATIBLE_PRODUCTS = 'Productos incompatibles'
}

// Distribución de canas por zonas
export interface GrayDistribution {
  frontal: number;
  lateral: number;
  crown: number;
  nape: number;
}

// Banda de demarcación
export interface DemarkationBand {
  location: number; // cm desde la raíz
  contrast: 'Bajo' | 'Medio' | 'Alto';
}

// Análisis de color por zona
export interface ZoneColorAnalysis {
  zone: HairZone;
  depthLevel: number; // 1-10
  tone: NaturalTone;
  undertone: Undertone;
  state: HairState;
  grayPercentage?: number; // Solo para raíces
  grayDistribution?: GrayDistribution; // Distribución detallada de canas
  grayType?: GrayHairType; // Tipo de canas
  grayPattern?: GrayPattern; // Patrón de canas
  previousChemical?: string; // Proceso químico previo
  damage?: 'Bajo' | 'Medio' | 'Alto';
  unwantedTone?: UnwantedTone; // Matiz no deseado presente
  pigmentAccumulation?: 'Baja' | 'Media' | 'Alta'; // Acumulación de pigmentos artificiales
  cuticleState?: CuticleState; // Estado de la cutícula
}

// Características físicas por zona
export interface ZonePhysicalAnalysis {
  zone: HairZone;
  porosity: HairPorosity;
  elasticity: HairElasticity;
  resistance: HairResistance;
  damage: 'Bajo' | 'Medio' | 'Alto';
}

// Resultado del test de seguridad
export interface SafetyTestResult {
  metalSalts: boolean;
  henna: boolean;
  incompatibleProducts: string[];
  testDate: string;
  notes?: string;
}

// Mediciones físicas del cabello
export interface HairMeasurements {
  totalLength: number; // cm
  demarkationLine?: number; // cm desde raíz
  monthlyGrowth?: number; // cm/mes promedio
  lastTouchUp?: string; // fecha
}

// Interfaz unificada para el diagnóstico capilar
export interface HairDiagnosis {
  // Características físicas generales
  thickness: HairThickness;
  density: HairDensity;
  
  // Análisis por zonas
  zoneColorAnalysis: ZoneColorAnalysis[];
  zonePhysicalAnalysis: ZonePhysicalAnalysis[];
  
  // Colorimetría general
  overallTone: NaturalTone;
  overallUndertone: Undertone;
  averageDepthLevel: number; // 1-10
  
  // Bandas de demarcación
  demarkationBands?: DemarkationBand[];
  
  // Mediciones físicas
  measurements?: HairMeasurements;
  
  // Información histórica
  lastChemicalProcess?: {
    type: string;
    date: string;
    products?: string[];
  };
  
  // Seguridad y compatibilidad
  safetyTests?: SafetyTestResult;
  detectedRisks?: HairRisk[];
  
  // Metadatos
  analysisType: AnalysisType;
  additionalNotes?: string;
  
  // Recomendaciones generadas
  recommendations?: HairRecommendations;
}

// Interfaz para recomendaciones
export interface HairRecommendations {
  products: string[];
  treatments: string[];
  precautions: string[];
  coloringSuggestions?: ColoringSuggestion[];
}

export interface ColoringSuggestion {
  targetColor: string;
  difficulty: 'Fácil' | 'Moderada' | 'Difícil';
  requiredProcesses: string[];
  estimatedSessions: number;
}

// Helpers para obtener opciones de selección
export const getHairThicknessOptions = () => Object.values(HairThickness);
export const getHairPorosityOptions = () => Object.values(HairPorosity);
export const getHairDensityOptions = () => Object.values(HairDensity);
export const getHairElasticityOptions = () => Object.values(HairElasticity);
export const getHairResistanceOptions = () => Object.values(HairResistance);
export const getNaturalToneOptions = () => Object.values(NaturalTone);
export const getUndertoneOptions = () => Object.values(Undertone);
export const getHairStateOptions = () => Object.values(HairState);
export const getHairZoneOptions = () => Object.values(HairZone);
export const getUnwantedToneOptions = () => Object.values(UnwantedTone);
export const getGrayHairTypeOptions = () => Object.values(GrayHairType);
export const getGrayPatternOptions = () => Object.values(GrayPattern);
export const getCuticleStateOptions = () => Object.values(CuticleState);
export const getHairRiskOptions = () => Object.values(HairRisk);
export const getDepthLevels = () => {
  const levels: number[] = [];
  for (let i = 1; i <= 10; i++) {
    for (let j = 0; j < 10; j++) {
      levels.push(Number((i + j * 0.1).toFixed(1)));
    }
  }
  return levels;
};

// Función para generar recomendaciones basadas en el diagnóstico
export function generateRecommendations(diagnosis: HairDiagnosis): HairRecommendations {
  const recommendations: HairRecommendations = {
    products: [],
    treatments: [],
    precautions: [],
    coloringSuggestions: []
  };

  // Analizar por zonas
  diagnosis.zonePhysicalAnalysis.forEach(zone => {
    // Recomendaciones basadas en porosidad por zona
    if (zone.porosity === HairPorosity.HIGH) {
      recommendations.products.push(`Productos selladores de cutícula para ${zone.zone.toLowerCase()}`);
      if (zone.zone === HairZone.ENDS) {
        recommendations.treatments.push('Corte de puntas + tratamiento sellador');
      }
    }

    // Recomendaciones basadas en elasticidad
    if (zone.elasticity === HairElasticity.POOR) {
      recommendations.treatments.push(`Tratamiento de reconstrucción en ${zone.zone.toLowerCase()}`);
      recommendations.precautions.push(`${zone.zone}: Riesgo de rotura - manejar con cuidado`);
    }

    // Daño alto
    if (zone.damage === 'Alto') {
      recommendations.precautions.push(`⚠️ Daño alto en ${zone.zone.toLowerCase()} - evitar procesos químicos agresivos`);
    }
  });

  // Análisis de color por zonas
  const rootAnalysis = diagnosis.zoneColorAnalysis.find(z => z.zone === HairZone.ROOTS);
  const hasMultipleTones = new Set(diagnosis.zoneColorAnalysis.map(z => z.depthLevel)).size > 1;
  
  if (hasMultipleTones) {
    recommendations.treatments.push('Igualación de color recomendada antes de nuevo proceso');
  }

  // Canas
  if (rootAnalysis?.grayPercentage && rootAnalysis.grayPercentage > 30) {
    recommendations.products.push('Fórmula con mayor poder cubriente para canas');
    recommendations.precautions.push(`${rootAnalysis.grayPercentage}% de canas - ajustar tiempo de procesamiento`);
  }

  // Estado del cabello
  const hasChemicalProcess = diagnosis.zoneColorAnalysis.some(z => 
    z.state !== HairState.NATURAL
  );
  
  if (hasChemicalProcess) {
    recommendations.treatments.push('Test de mechón obligatorio antes de aplicación completa');
    
    const bleachedZones = diagnosis.zoneColorAnalysis.filter(z => z.state === HairState.BLEACHED);
    if (bleachedZones.length > 0) {
      recommendations.precautions.push('⚠️ Cabello decolorado presente - extremar precauciones');
      recommendations.products.push('Usar productos de pH balanceado');
    }
  }

  // Recomendaciones basadas en densidad
  if (diagnosis.density === HairDensity.LOW) {
    recommendations.products.push('Productos voluminizadores sin peso');
    recommendations.treatments.push('Evitar productos pesados que aplasten el cabello');
  }

  // Diferencia de niveles
  const depthLevels = diagnosis.zoneColorAnalysis.map(z => z.depthLevel);
  const maxDifference = Math.max(...depthLevels) - Math.min(...depthLevels);
  
  if (maxDifference > 3) {
    recommendations.coloringSuggestions?.push({
      targetColor: 'Igualación de color',
      difficulty: 'Difícil',
      requiredProcesses: ['Pre-pigmentación en zonas claras', 'Aplicación diferenciada por zonas'],
      estimatedSessions: 2
    });
  }

  // Proceso químico reciente
  if (diagnosis.lastChemicalProcess) {
    const daysSince = Math.floor((Date.now() - new Date(diagnosis.lastChemicalProcess.date).getTime()) / (1000 * 60 * 60 * 24));
    if (daysSince < 14) {
      recommendations.precautions.push(`⚠️ Último proceso hace ${daysSince} días - esperar mínimo 2 semanas`);
    }
  }

  return recommendations;
}