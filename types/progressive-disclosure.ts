/**
 * Progressive Disclosure System Types
 * Sistema de revelación progresiva para simplificar la UX
 */

export interface DisclosureLevel {
  id: string;
  name: string;
  description: string;
  priority: number;
  estimatedTime: number; // segundos
  requiredFields: string[];
  optionalFields: string[];
  dependencies?: string[]; // IDs de otros niveles requeridos
}

export interface FieldConfiguration {
  id: string;
  type: 'text' | 'select' | 'visual-select' | 'slider' | 'toggle' | 'color-picker' | 'hair-map';
  label: string;
  description?: string;
  placeholder?: string;
  required: boolean;
  level: number; // 1, 2, o 3
  showCondition?: (data: any) => boolean;
  validationRules?: ValidationRule[];
  aiSuggestion?: boolean; // Si la IA puede pre-llenar este campo
  contextualHelp?: string;
}

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any, allData: any) => boolean;
}

export interface ProgressiveFormState {
  currentLevel: number;
  completedLevels: number[];
  fieldValues: Record<string, any>;
  validationErrors: Record<string, string>;
  aiSuggestions: Record<string, any>;
  isValid: boolean;
  canProceed: boolean;
}

export interface SmartFormConfig {
  id: string;
  title: string;
  description: string;
  levels: DisclosureLevel[];
  fields: FieldConfiguration[];
  aiAnalysisRequired: boolean;
  estimatedTotalTime: number;
}

// Configuraciones específicas para coloración
export interface ColorConsultationConfig extends SmartFormConfig {
  type: 'current-color' | 'desired-color' | 'full-consultation';
  requiresPhoto: boolean;
  viabilityCheck: boolean;
}

// Estados de confianza de la IA
export interface AIConfidenceScore {
  overall: number; // 0-100
  fieldScores: Record<string, number>;
  reasoning: string[];
  suggestions: string[];
}

// Contexto de la consulta
export interface ConsultationContext {
  clientHistory?: any;
  previousServices?: any[];
  currentHairState?: any;
  professionalLevel?: 'beginner' | 'intermediate' | 'expert';
  timeConstraints?: number; // minutos disponibles
  budgetConstraints?: number;
}

// Eventos del sistema
export interface ProgressiveDisclosureEvent {
  type: 'level_completed' | 'field_changed' | 'validation_error' | 'ai_suggestion' | 'help_requested';
  timestamp: Date;
  data: any;
  level: number;
  fieldId?: string;
}

// Configuración de visualización
export interface UIConfiguration {
  showProgressBar: boolean;
  showTimeEstimate: boolean;
  showConfidenceScores: boolean;
  allowLevelSkipping: boolean;
  autoSave: boolean;
  hapticFeedback: boolean;
  animations: boolean;
}

// Métricas y analytics
export interface FormMetrics {
  startTime: Date;
  completionTime?: Date;
  timePerLevel: Record<number, number>;
  fieldsModified: string[];
  aiSuggestionsAccepted: number;
  aiSuggestionsRejected: number;
  helpRequestsCount: number;
  validationErrorsCount: number;
  abandonmentPoint?: number; // nivel donde se abandonó
}

// Configuraciones predefinidas
export const DISCLOSURE_LEVELS: Record<string, DisclosureLevel> = {
  QUICK_START: {
    id: 'quick_start',
    name: 'Inicio Rápido',
    description: 'Información esencial para comenzar',
    priority: 1,
    estimatedTime: 30,
    requiredFields: ['goal', 'current_photo'],
    optionalFields: []
  },
  SMART_CONSULTATION: {
    id: 'smart_consultation',
    name: 'Consulta Inteligente',
    description: 'Análisis detallado con IA',
    priority: 2,
    estimatedTime: 120,
    requiredFields: ['desired_color', 'viability_check'],
    optionalFields: ['technique_preference', 'maintenance_level'],
    dependencies: ['quick_start']
  },
  EXPERT_MODE: {
    id: 'expert_mode',
    name: 'Modo Experto',
    description: 'Configuración avanzada y personalización',
    priority: 3,
    estimatedTime: 180,
    requiredFields: [],
    optionalFields: ['zone_analysis', 'chemical_history', 'advanced_preferences'],
    dependencies: ['smart_consultation']
  }
};

// Tipos de campos visuales
export interface VisualSelectOption {
  id: string;
  label: string;
  description?: string;
  image?: string;
  color?: string;
  icon?: string;
  confidence?: number; // Para sugerencias de IA
}

export interface ColorPickerConfig {
  type: 'wheel' | 'palette' | 'levels';
  viabilityFilter: boolean;
  showConfidence: boolean;
  allowCustomColors: boolean;
}

export interface HairMapConfig {
  zones: string[];
  interactiveMode: boolean;
  showAnalysis: boolean;
  allowZoneSelection: boolean;
}

// Configuración de validación inteligente
export interface SmartValidation {
  realTimeValidation: boolean;
  contextualMessages: boolean;
  preventiveGuidance: boolean;
  autoCorrection: boolean;
}
