/**
 * Progressive Disclosure Service
 * Servicio principal para manejar la revelación progresiva de formularios
 */

import {
  DisclosureLevel,
  FieldConfiguration,
  ProgressiveFormState,
  SmartFormConfig,
  ColorConsultationConfig,
  AIConfidenceScore,
  ConsultationContext,
  FormMetrics,
  VisualSelectOption,
  DISCLOSURE_LEVELS
} from '@/types/progressive-disclosure';
import { HairDiagnosis, ZoneColorAnalysis } from '@/types/hair-diagnosis';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';

export class ProgressiveDisclosureService {
  private static instance: ProgressiveDisclosureService;
  private formConfigs: Map<string, SmartFormConfig> = new Map();
  private activeStates: Map<string, ProgressiveFormState> = new Map();
  private metrics: Map<string, FormMetrics> = new Map();

  static getInstance(): ProgressiveDisclosureService {
    if (!ProgressiveDisclosureService.instance) {
      ProgressiveDisclosureService.instance = new ProgressiveDisclosureService();
    }
    return ProgressiveDisclosureService.instance;
  }

  /**
   * Inicializa una nueva sesión de formulario progresivo
   */
  initializeForm(
    formId: string, 
    config: SmartFormConfig, 
    context?: ConsultationContext
  ): ProgressiveFormState {
    // Configurar el formulario
    this.formConfigs.set(formId, config);
    
    // Estado inicial
    const initialState: ProgressiveFormState = {
      currentLevel: 1,
      completedLevels: [],
      fieldValues: {},
      validationErrors: {},
      aiSuggestions: {},
      isValid: false,
      canProceed: false
    };

    // Aplicar contexto si está disponible
    if (context) {
      this.applyContext(formId, initialState, context);
    }

    this.activeStates.set(formId, initialState);
    
    // Inicializar métricas
    this.metrics.set(formId, {
      startTime: new Date(),
      timePerLevel: {},
      fieldsModified: [],
      aiSuggestionsAccepted: 0,
      aiSuggestionsRejected: 0,
      helpRequestsCount: 0,
      validationErrorsCount: 0
    });

    return initialState;
  }

  /**
   * Obtiene los campos visibles para el nivel actual
   */
  getVisibleFields(formId: string): FieldConfiguration[] {
    const config = this.formConfigs.get(formId);
    const state = this.activeStates.get(formId);
    
    if (!config || !state) return [];

    const currentLevel = state.currentLevel;
    const completedLevels = state.completedLevels;

    return config.fields.filter(field => {
      // Mostrar campos del nivel actual y niveles completados
      if (field.level <= currentLevel || completedLevels.includes(field.level)) {
        // Verificar condiciones de visibilidad
        if (field.showCondition) {
          return field.showCondition(state.fieldValues);
        }
        return true;
      }
      return false;
    });
  }

  /**
   * Actualiza el valor de un campo y recalcula el estado
   */
  updateField(formId: string, fieldId: string, value: any): ProgressiveFormState {
    const state = this.activeStates.get(formId);
    if (!state) throw new Error(`Form ${formId} not found`);

    // Actualizar valor
    state.fieldValues[fieldId] = value;
    
    // Registrar modificación
    const metrics = this.metrics.get(formId);
    if (metrics && !metrics.fieldsModified.includes(fieldId)) {
      metrics.fieldsModified.push(fieldId);
    }

    // Limpiar errores de validación para este campo
    delete state.validationErrors[fieldId];

    // Recalcular validaciones
    this.validateForm(formId);

    // Verificar si se puede avanzar de nivel
    this.checkLevelCompletion(formId);

    return state;
  }

  /**
   * Aplica sugerencias de IA a los campos
   */
  applyAISuggestions(
    formId: string, 
    suggestions: Record<string, any>, 
    confidence: AIConfidenceScore
  ): void {
    const state = this.activeStates.get(formId);
    if (!state) return;

    // Aplicar sugerencias con alta confianza automáticamente
    Object.entries(suggestions).forEach(([fieldId, value]) => {
      const fieldConfidence = confidence.fieldScores[fieldId] || 0;
      
      if (fieldConfidence > 80) {
        // Auto-aplicar con alta confianza
        state.fieldValues[fieldId] = value;
        state.aiSuggestions[fieldId] = { value, confidence: fieldConfidence, applied: true };
      } else if (fieldConfidence > 50) {
        // Sugerir para revisión manual
        state.aiSuggestions[fieldId] = { value, confidence: fieldConfidence, applied: false };
      }
    });

    this.validateForm(formId);
  }

  /**
   * Acepta una sugerencia de IA
   */
  acceptAISuggestion(formId: string, fieldId: string): void {
    const state = this.activeStates.get(formId);
    if (!state || !state.aiSuggestions[fieldId]) return;

    const suggestion = state.aiSuggestions[fieldId];
    state.fieldValues[fieldId] = suggestion.value;
    suggestion.applied = true;

    // Actualizar métricas
    const metrics = this.metrics.get(formId);
    if (metrics) {
      metrics.aiSuggestionsAccepted++;
    }

    this.validateForm(formId);
  }

  /**
   * Rechaza una sugerencia de IA
   */
  rejectAISuggestion(formId: string, fieldId: string): void {
    const state = this.activeStates.get(formId);
    if (!state) return;

    delete state.aiSuggestions[fieldId];

    // Actualizar métricas
    const metrics = this.metrics.get(formId);
    if (metrics) {
      metrics.aiSuggestionsRejected++;
    }
  }

  /**
   * Avanza al siguiente nivel
   */
  advanceToNextLevel(formId: string): boolean {
    const state = this.activeStates.get(formId);
    const config = this.formConfigs.get(formId);
    
    if (!state || !config) return false;

    const currentLevel = state.currentLevel;
    
    // Verificar que el nivel actual esté completo
    if (!this.isLevelComplete(formId, currentLevel)) {
      return false;
    }

    // Marcar nivel como completado
    if (!state.completedLevels.includes(currentLevel)) {
      state.completedLevels.push(currentLevel);
    }

    // Avanzar al siguiente nivel
    const maxLevel = Math.max(...config.levels.map(l => l.priority));
    if (currentLevel < maxLevel) {
      state.currentLevel = currentLevel + 1;
      
      // Registrar tiempo en el nivel
      const metrics = this.metrics.get(formId);
      if (metrics) {
        const now = Date.now();
        const levelStartTime = metrics.timePerLevel[currentLevel] || metrics.startTime.getTime();
        metrics.timePerLevel[currentLevel] = now - levelStartTime;
        metrics.timePerLevel[state.currentLevel] = now;
      }
      
      return true;
    }

    return false;
  }

  /**
   * Retrocede al nivel anterior
   */
  goToPreviousLevel(formId: string): boolean {
    const state = this.activeStates.get(formId);
    if (!state || state.currentLevel <= 1) return false;

    state.currentLevel = state.currentLevel - 1;
    return true;
  }

  /**
   * Salta a un nivel específico (si está permitido)
   */
  jumpToLevel(formId: string, targetLevel: number): boolean {
    const state = this.activeStates.get(formId);
    const config = this.formConfigs.get(formId);
    
    if (!state || !config) return false;

    // Verificar que se pueden saltar niveles
    const targetLevelConfig = config.levels.find(l => l.priority === targetLevel);
    if (!targetLevelConfig) return false;

    // Verificar dependencias
    if (targetLevelConfig.dependencies) {
      const hasAllDependencies = targetLevelConfig.dependencies.every(depId => {
        const depLevel = config.levels.find(l => l.id === depId);
        return depLevel && state.completedLevels.includes(depLevel.priority);
      });
      
      if (!hasAllDependencies) return false;
    }

    state.currentLevel = targetLevel;
    return true;
  }

  /**
   * Valida el formulario completo
   */
  private validateForm(formId: string): void {
    const state = this.activeStates.get(formId);
    const config = this.formConfigs.get(formId);
    
    if (!state || !config) return;

    const visibleFields = this.getVisibleFields(formId);
    const errors: Record<string, string> = {};

    visibleFields.forEach(field => {
      const value = state.fieldValues[field.id];
      
      if (field.required && (!value || value === '')) {
        errors[field.id] = `${field.label} es requerido`;
        return;
      }

      if (field.validationRules && value) {
        field.validationRules.forEach(rule => {
          if (!this.validateRule(rule, value, state.fieldValues)) {
            errors[field.id] = rule.message;
          }
        });
      }
    });

    state.validationErrors = errors;
    state.isValid = Object.keys(errors).length === 0;
  }

  /**
   * Verifica si un nivel está completo
   */
  private isLevelComplete(formId: string, level: number): boolean {
    const config = this.formConfigs.get(formId);
    const state = this.activeStates.get(formId);
    
    if (!config || !state) return false;

    const levelConfig = config.levels.find(l => l.priority === level);
    if (!levelConfig) return false;

    // Verificar campos requeridos del nivel
    return levelConfig.requiredFields.every(fieldId => {
      const value = state.fieldValues[fieldId];
      return value !== undefined && value !== null && value !== '';
    });
  }

  /**
   * Verifica si se puede completar el nivel actual
   */
  private checkLevelCompletion(formId: string): void {
    const state = this.activeStates.get(formId);
    if (!state) return;

    const canComplete = this.isLevelComplete(formId, state.currentLevel);
    state.canProceed = canComplete && state.isValid;
  }

  /**
   * Aplica contexto inicial al formulario
   */
  private applyContext(
    formId: string, 
    state: ProgressiveFormState, 
    context: ConsultationContext
  ): void {
    // Aplicar datos del historial del cliente
    if (context.clientHistory) {
      // Pre-llenar campos basados en historial
      // Implementar lógica específica según el contexto
    }

    // Ajustar configuración según nivel profesional
    if (context.professionalLevel === 'expert') {
      // Permitir saltar niveles para expertos
      state.currentLevel = 2; // Comenzar en nivel avanzado
    }
  }

  /**
   * Valida una regla específica
   */
  private validateRule(rule: any, value: any, allData: any): boolean {
    switch (rule.type) {
      case 'required':
        return value !== undefined && value !== null && value !== '';
      case 'min':
        return typeof value === 'number' ? value >= rule.value : value.length >= rule.value;
      case 'max':
        return typeof value === 'number' ? value <= rule.value : value.length <= rule.value;
      case 'pattern':
        return new RegExp(rule.value).test(value);
      case 'custom':
        return rule.validator ? rule.validator(value, allData) : true;
      default:
        return true;
    }
  }

  /**
   * Obtiene métricas del formulario
   */
  getMetrics(formId: string): FormMetrics | undefined {
    return this.metrics.get(formId);
  }

  /**
   * Completa el formulario
   */
  completeForm(formId: string): FormMetrics | undefined {
    const metrics = this.metrics.get(formId);
    if (metrics) {
      metrics.completionTime = new Date();
    }
    
    // Limpiar estado activo
    this.activeStates.delete(formId);
    
    return metrics;
  }
}
