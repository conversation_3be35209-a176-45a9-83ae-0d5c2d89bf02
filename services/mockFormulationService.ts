import { RegionalConfig } from '@/types/regional';
import { HairDiagnosis } from '@/types/hair-diagnosis';
import { DesiredAnalysisResult } from '@/types/desired-analysis';
import { ColorFormula } from '@/types/formulation';

interface FormulationContext {
  currentDiagnosis: HairDiagnosis;
  desiredResult: DesiredAnalysisResult;
  brand: string;
  line: string;
  regionalConfig: RegionalConfig;
  clientHistory?: string;
  conversionMode?: {
    originalBrand: string;
    originalLine: string;
    originalFormula: string;
  };
}

export class MockFormulationService {
  /**
   * Generates a mock formula considering regional configuration
   * This will be replaced with OpenAI API calls in the future
   */
  static async generateFormula(context: FormulationContext): Promise<string> {
    // Validate required parameters
    if (!context) {
      throw new Error('Contexto de formulación no proporcionado');
    }

    const {
      currentDiagnosis,
      desiredResult,
      brand,
      line,
      regionalConfig,
      clientHistory,
      conversionMode
    } = context;

    // Validate essential fields
    if (!currentDiagnosis) {
      throw new Error('Diagnóstico actual no proporcionado');
    }
    
    if (!desiredResult || !desiredResult.general) {
      throw new Error('Resultado deseado no proporcionado o incompleto');
    }
    
    if (!brand || !line) {
      throw new Error('Marca o línea no especificada');
    }
    
    if (!regionalConfig) {
      throw new Error('Configuración regional no disponible');
    }

    // Log for debugging
    console.log('[MockFormulationService] Generating formula with:', {
      brand,
      line,
      technique: desiredResult.general?.technique,
      regionalConfig: regionalConfig.countryCode,
      language: regionalConfig.language
    });

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Get terminology based on region
    const colorTerm = regionalConfig.colorTerminology;
    const developerTerm = regionalConfig.developerTerminology;
    const volumeUnit = regionalConfig.volumeUnit;
    const weightUnit = regionalConfig.weightUnit;

    // Calculate levels with safe defaults
    const currentLevel = currentDiagnosis.zoneAnalysis?.roots?.depthLevel || 5;
    const targetLevel = parseInt(desiredResult.general?.overallLevel || '8') || 8;
    const levelDifference = Math.abs(targetLevel - currentLevel);

    // Volume conversions if needed
    const getVolume = (mlValue: number): string => {
      if (regionalConfig.measurementSystem === 'imperial' && volumeUnit === 'fl oz') {
        const ozValue = mlValue * 0.033814;
        return `${ozValue.toFixed(1)} ${volumeUnit}`;
      }
      return `${mlValue}${volumeUnit}`;
    };

    const getWeight = (gValue: number): string => {
      if (regionalConfig.measurementSystem === 'imperial' && weightUnit === 'oz') {
        const ozValue = gValue * 0.035274;
        return `${ozValue.toFixed(1)} ${weightUnit}`;
      }
      return `${gValue}${weightUnit}`;
    };

    // Build formula header
    let formula = '';
    
    if (conversionMode) {
      formula += this.getConversionHeader(conversionMode, brand, line, regionalConfig);
    }

    // Add regional considerations
    if (regionalConfig.requiresAllergyTest) {
      formula += `⚠️ ${this.getLocalizedText('allergyTestRequired', regionalConfig.language)}\n\n`;
    }

    // Formula components
    formula += `${brand} ${line}:\n\n`;
    
    // Main formula based on technique with safe default
    const technique = desiredResult.general?.technique || 'full_color';
    
    console.log('[MockFormulationService] Using technique:', technique);
    
    if (technique === 'balayage') {
      formula += this.getBalayageFormula(targetLevel, brand, line, getWeight, getVolume, developerTerm, regionalConfig);
    } else if (technique === 'highlights') {
      formula += this.getHighlightsFormula(targetLevel, brand, line, getWeight, getVolume, developerTerm, regionalConfig);
    } else {
      formula += this.getFullColorFormula(targetLevel, levelDifference, brand, line, getWeight, getVolume, developerTerm, regionalConfig);
    }

    // Add processing time
    formula += `\n${this.getLocalizedText('processingTime', regionalConfig.language)}: `;
    formula += this.getProcessingTime(levelDifference, regionalConfig);

    // Add application steps
    formula += `\n\n${this.getLocalizedText('applicationSteps', regionalConfig.language)}:\n`;
    formula += this.getApplicationSteps(technique, levelDifference, regionalConfig);

    // Add notes if converted
    if (conversionMode) {
      formula += `\n\n${this.getLocalizedText('conversionNotes', regionalConfig.language)}:\n`;
      formula += this.getConversionNotes(regionalConfig);
    }

    // Add regulatory notes
    if (regionalConfig.maxDeveloperVolume < 40 && levelDifference > 3) {
      formula += `\n\n⚠️ ${this.getLocalizedText('developerLimitation', regionalConfig.language)} ${regionalConfig.maxDeveloperVolume}vol`;
    }

    return formula;
  }

  private static getConversionHeader(
    conversionMode: FormulationContext['conversionMode'],
    brand: string,
    line: string,
    regional: RegionalConfig
  ): string {
    return `═══════════════════════════════════════
🔄 ${this.getLocalizedText('formulaConverted', regional.language).toUpperCase()}
═══════════════════════════════════════
${this.getLocalizedText('original', regional.language)}: ${conversionMode?.originalBrand} ${conversionMode?.originalLine} - ${conversionMode?.originalFormula}
${this.getLocalizedText('equivalent', regional.language)}: ${brand} ${line}
═══════════════════════════════════════\n\n`;
  }

  private static getBalayageFormula(
    targetLevel: number,
    brand: string,
    line: string,
    getWeight: (g: number) => string,
    getVolume: (ml: number) => string,
    developerTerm: string,
    regional: RegionalConfig
  ): string {
    return `${this.getLocalizedText('formulaFor', regional.language)} Balayage:
- ${line} ${targetLevel}/1 (${getWeight(30)})
- ${line} ${targetLevel}/69 (${getWeight(10)})
- ${developerTerm} 30 vol (${getVolume(40)}) ${this.getLocalizedText('forMidsEnds', regional.language)}
- ${developerTerm} 20 vol (${getVolume(40)}) ${this.getLocalizedText('forRoots', regional.language)}\n`;
  }

  private static getHighlightsFormula(
    targetLevel: number,
    brand: string,
    line: string,
    getWeight: (g: number) => string,
    getVolume: (ml: number) => string,
    developerTerm: string,
    regional: RegionalConfig
  ): string {
    return `${this.getLocalizedText('formulaFor', regional.language)} ${this.getLocalizedText('highlights', regional.language)}:
- ${this.getLocalizedText('bleachPowder', regional.language)} (${getWeight(30)})
- ${developerTerm} 30 vol (${getVolume(60)})
- Olaplex N°1 (${getVolume(3.75)})

${this.getLocalizedText('forToning', regional.language)}:
- ${line} ${targetLevel}/81 (${getWeight(20)})
- ${line} ${targetLevel}/69 (${getWeight(10)})
- ${developerTerm} 10 vol (${getVolume(30)})\n`;
  }

  private static getFullColorFormula(
    targetLevel: number,
    levelDifference: number,
    brand: string,
    line: string,
    getWeight: (g: number) => string,
    getVolume: (ml: number) => string,
    developerTerm: string,
    regional: RegionalConfig
  ): string {
    const baseAmount = levelDifference > 2 ? 40 : 30;
    const mixAmount = levelDifference > 2 ? 20 : 10;
    const developerVolume = levelDifference > 2 ? 30 : 20;
    const developerAmount = levelDifference > 2 ? 90 : 60;

    let formula = `${this.getLocalizedText('fullColorFormula', regional.language)}:
- ${line} ${targetLevel}/1 (${getWeight(baseAmount)})
- ${line} ${targetLevel}/69 (${getWeight(mixAmount)})
- ${developerTerm} ${developerVolume} vol (${getVolume(developerAmount)})`;

    // Add bond builders for significant level changes
    if (levelDifference > 2) {
      const bondBuilders = ['Olaplex N°1', 'Bond Ultim8 N°1', 'K18 Leave-in Treatment', 'WellaPlex N°1'];
      const selectedBond = bondBuilders[Math.floor(Math.random() * bondBuilders.length)];
      formula += `\n- ${selectedBond} (${getVolume(selectedBond.includes('K18') ? 2 : 5)})`;
    }

    // Add violet mix for neutralization if needed
    if (targetLevel >= 8) {
      formula += `\n- Mix Violeta (2cm)`;
    }

    return formula + '\n';
  }

  private static getProcessingTime(levelDifference: number, regional: RegionalConfig): string {
    const baseTime = levelDifference > 2 ? 45 : 35;
    
    if (regional.timeFormat === '12h') {
      const hours = Math.floor(baseTime / 60);
      const minutes = baseTime % 60;
      if (hours > 0) {
        return `${hours}h ${minutes}min`;
      }
      return `${minutes} min`;
    }
    
    return `${baseTime} ${this.getLocalizedText('minutes', regional.language)}`;
  }

  private static getApplicationSteps(
    technique: string,
    levelDifference: number,
    regional: RegionalConfig
  ): string {
    const lang = regional.language;
    
    if (technique === 'balayage') {
      return `1. ${this.getLocalizedText('sectionHairV', lang)}
2. ${this.getLocalizedText('applyBrushMidsEnds', lang)}
3. ${this.getLocalizedText('leaveRootsNatural', lang)}
4. ${this.getLocalizedText('wrapSelectiveFoil', lang)}`;
    }
    
    if (technique === 'highlights') {
      return `1. ${this.getLocalizedText('selectFineSections', lang)}
2. ${this.getLocalizedText('applyBleachRootToTip', lang)}
3. ${this.getLocalizedText('wrapInFoil', lang)}
4. ${this.getLocalizedText('processUntilDesired', lang)}
5. ${this.getLocalizedText('toneAsNeeded', lang)}`;
    }
    
    // Full color
    const rootTime = levelDifference > 2 ? 25 : 20;
    const additionalTime = levelDifference > 2 ? 20 : 15;
    
    return `1. ${this.getLocalizedText('applyRootsFirst', lang)}, ${this.getLocalizedText('leave', lang)} ${rootTime} ${this.getLocalizedText('minutes', lang)}
2. ${this.getLocalizedText('extendMidsEnds', lang)} ${additionalTime} ${this.getLocalizedText('additionalMinutes', lang)}
3. ${this.getLocalizedText('emulsifyBeforeRinse', lang)}`;
  }

  private static getConversionNotes(regional: RegionalConfig): string {
    const lang = regional.language;
    return `• ${this.getLocalizedText('conversionConfidence', lang)}: 85%
• ${this.getLocalizedText('recommendStrandTest', lang)}
• ${this.getLocalizedText('adjustTimeAsNeeded', lang)}`;
  }

  /**
   * Localization helper - returns text in the appropriate language
   * In production, this would use a proper i18n library
   */
  private static getLocalizedText(key: string, language?: string): string {
    // Default to Spanish if no language provided
    const lang = language || 'es';
    
    const translations: Record<string, Record<string, string>> = {
      es: {
        allergyTestRequired: 'Prueba de alergia obligatoria 48h antes',
        processingTime: 'Tiempo de proceso',
        applicationSteps: 'Pasos de aplicación',
        conversionNotes: 'Notas de conversión',
        developerLimitation: 'Limitación legal de oxidante en este país:',
        formulaConverted: 'Fórmula Adaptada',
        original: 'Original',
        equivalent: 'Equivalente',
        formulaFor: 'Fórmula para',
        forMidsEnds: 'para medios/puntas',
        forRoots: 'para raíces',
        highlights: 'Mechas',
        bleachPowder: 'Polvo decolorante',
        forToning: 'Para tonalizar',
        fullColorFormula: 'Fórmula Tinte Completo',
        minutes: 'minutos',
        sectionHairV: 'Seccionar el cabello en V',
        applyBrushMidsEnds: 'Aplicar con pincel desde medios hacia puntas',
        leaveRootsNatural: 'Dejar raíces naturales o aplicar fórmula más suave',
        wrapSelectiveFoil: 'Envolver en papel aluminio selectivamente',
        selectFineSections: 'Seleccionar mechones finos con peine de cola',
        applyBleachRootToTip: 'Aplicar decolorante desde raíz a puntas',
        wrapInFoil: 'Envolver en papel aluminio',
        processUntilDesired: 'Procesar hasta alcanzar fondo de aclaración deseado',
        toneAsNeeded: 'Tonalizar según necesidad',
        applyRootsFirst: 'Aplicar primero en raíces',
        leave: 'dejar',
        extendMidsEnds: 'Extender a medios y puntas por',
        additionalMinutes: 'minutos adicionales',
        emulsifyBeforeRinse: 'Emulsionar con agua tibia antes de enjuagar',
        conversionConfidence: 'Confianza de conversión',
        recommendStrandTest: 'Se recomienda prueba de mechón',
        adjustTimeAsNeeded: 'Ajustar tiempo según necesidad',
      },
      en: {
        allergyTestRequired: 'Allergy test required 48h before',
        processingTime: 'Processing time',
        applicationSteps: 'Application steps',
        conversionNotes: 'Conversion notes',
        developerLimitation: 'Legal developer limit in this country:',
        formulaConverted: 'Formula Converted',
        original: 'Original',
        equivalent: 'Equivalent',
        formulaFor: 'Formula for',
        forMidsEnds: 'for mids/ends',
        forRoots: 'for roots',
        highlights: 'Highlights',
        bleachPowder: 'Bleaching powder',
        forToning: 'For toning',
        fullColorFormula: 'Full Color Formula',
        minutes: 'minutes',
        sectionHairV: 'Section hair in V pattern',
        applyBrushMidsEnds: 'Apply with brush from mids to ends',
        leaveRootsNatural: 'Leave roots natural or apply lighter formula',
        wrapSelectiveFoil: 'Wrap selectively in foil',
        selectFineSections: 'Select fine sections with tail comb',
        applyBleachRootToTip: 'Apply bleach from root to tip',
        wrapInFoil: 'Wrap in foil',
        processUntilDesired: 'Process until desired lift achieved',
        toneAsNeeded: 'Tone as needed',
        applyRootsFirst: 'Apply to roots first',
        leave: 'leave',
        extendMidsEnds: 'Extend to mids and ends for',
        additionalMinutes: 'additional minutes',
        emulsifyBeforeRinse: 'Emulsify with warm water before rinsing',
        conversionConfidence: 'Conversion confidence',
        recommendStrandTest: 'Strand test recommended',
        adjustTimeAsNeeded: 'Adjust timing as needed',
      },
      pt: {
        allergyTestRequired: 'Teste de alergia obrigatório 48h antes',
        processingTime: 'Tempo de processamento',
        applicationSteps: 'Passos de aplicação',
        conversionNotes: 'Notas de conversão',
        developerLimitation: 'Limite legal de oxidante neste país:',
        formulaConverted: 'Fórmula Convertida',
        original: 'Original',
        equivalent: 'Equivalente',
        formulaFor: 'Fórmula para',
        forMidsEnds: 'para meio/pontas',
        forRoots: 'para raízes',
        highlights: 'Mechas',
        bleachPowder: 'Pó descolorante',
        forToning: 'Para tonalizar',
        fullColorFormula: 'Fórmula Coloração Completa',
        minutes: 'minutos',
        sectionHairV: 'Seccionar o cabelo em V',
        applyBrushMidsEnds: 'Aplicar com pincel do meio às pontas',
        leaveRootsNatural: 'Deixar raízes naturais ou aplicar fórmula mais suave',
        wrapSelectiveFoil: 'Envolver seletivamente em papel alumínio',
        selectFineSections: 'Selecionar mechas finas com pente de cauda',
        applyBleachRootToTip: 'Aplicar descolorante da raiz às pontas',
        wrapInFoil: 'Envolver em papel alumínio',
        processUntilDesired: 'Processar até alcançar o fundo de clareamento desejado',
        toneAsNeeded: 'Tonalizar conforme necessário',
        applyRootsFirst: 'Aplicar primeiro nas raízes',
        leave: 'deixar',
        extendMidsEnds: 'Estender para meio e pontas por',
        additionalMinutes: 'minutos adicionais',
        emulsifyBeforeRinse: 'Emulsionar com água morna antes de enxaguar',
        conversionConfidence: 'Confiança da conversão',
        recommendStrandTest: 'Teste de mecha recomendado',
        adjustTimeAsNeeded: 'Ajustar tempo conforme necessário',
      },
      fr: {
        allergyTestRequired: 'Test d\'allergie obligatoire 48h avant',
        processingTime: 'Temps de pose',
        applicationSteps: 'Étapes d\'application',
        conversionNotes: 'Notes de conversion',
        developerLimitation: 'Limite légale d\'oxydant dans ce pays:',
        formulaConverted: 'Formule Convertie',
        original: 'Original',
        equivalent: 'Équivalent',
        formulaFor: 'Formule pour',
        forMidsEnds: 'pour longueurs/pointes',
        forRoots: 'pour racines',
        highlights: 'Mèches',
        bleachPowder: 'Poudre décolorante',
        forToning: 'Pour patiner',
        fullColorFormula: 'Formule Coloration Complète',
        minutes: 'minutes',
        sectionHairV: 'Sectionner les cheveux en V',
        applyBrushMidsEnds: 'Appliquer au pinceau des longueurs aux pointes',
        leaveRootsNatural: 'Laisser racines naturelles ou appliquer formule plus douce',
        wrapSelectiveFoil: 'Envelopper sélectivement dans papier aluminium',
        selectFineSections: 'Sélectionner mèches fines avec peigne queue',
        applyBleachRootToTip: 'Appliquer décoloration racine à pointe',
        wrapInFoil: 'Envelopper dans papier aluminium',
        processUntilDesired: 'Laisser agir jusqu\'à éclaircissement désiré',
        toneAsNeeded: 'Patiner selon besoin',
        applyRootsFirst: 'Appliquer d\'abord sur racines',
        leave: 'laisser',
        extendMidsEnds: 'Étendre sur longueurs et pointes pendant',
        additionalMinutes: 'minutes supplémentaires',
        emulsifyBeforeRinse: 'Émulsionner à l\'eau tiède avant rinçage',
        conversionConfidence: 'Confiance de conversion',
        recommendStrandTest: 'Test mèche recommandé',
        adjustTimeAsNeeded: 'Ajuster temps selon besoin',
      },
    };

    const langTranslations = translations[lang] || translations['es'];
    return langTranslations[key] || translations['es'][key] || translations['en'][key] || key;
  }
}