# Flujo de Trabajo Estándar para Claude Code

## Wizard de Seguridad (2025-07-06)

### Implementación del Wizard de Seguridad de 4 pasos
La aplicación incluye un wizard de seguridad completo antes de iniciar cualquier servicio de coloración.

#### Estructura:
1. **Paso 1 - Checklist de Seguridad**: Verificación de protocolos básicos
2. **Paso 2 - Test de Parche**: Verificación de alergias (48h antes)
3. **Paso 3 - Verificaciones Críticas**: Test de sales metálicas, henna, formol
4. **Paso 4 - Consentimiento**: Firma digital y aceptación de términos

#### Configuración:
- En `settings.tsx` existe un toggle "Saltar Verificación de Seguridad"
- Al activarlo, se muestra advertencia legal
- Si está activado, el flujo salta directo a `/service/new`
- Si está desactivado, pasa por `/service/safety-verification`

#### Datos críticos verificados:
- **Sales metálicas**: Test obligatorio con H₂O₂ si hay historial químico
- **Henna**: Incompatible con procesos químicos
- **Formaldehído**: Verificación de alisados previos
- **Remedios caseros**: Limón, manzanilla, vinagre, etc.

#### Integración:
- Los resultados se guardan en `consentRecord` del historial del cliente
- Incluye `criticalChecks` con todos los resultados de las verificaciones
- La firma digital se almacena como base64

## Lecciones Aprendidas

### Duplicación de tabs en diagnóstico (2025-07-08)
**Problema**: Los tabs de "Raíces", "Medios" y "Puntas" aparecían duplicados en la interfaz de diagnóstico
**Causa**: Código duplicado envuelto en un fragmento React innecesario
**Solución**: Eliminar el bloque de código duplicado (líneas 1880-1931 en `app/service/new.tsx`)

### Vistas sin funcionalidad (2025-07-08)
**Problema**: Botones "Vista Mapa" y "Vista Detallada" aparecían en la UI pero sin funcionalidad real
**Estado**: No se encontraron en el código actual - posiblemente ya eliminados o requieren recarga de la app
**Acción**: Si persisten después de recargar, investigar más a fondo

### Visualización de costes y rentabilidad (2025-07-08)
**Problema**: El desglose de costes y rentabilidad no aparecía al generar fórmulas
**Causa**: Se había modificado incorrectamente `calculateFormulaCost` para retornar `null` en "solo-formulas"
**Solución**: Restaurar el comportamiento original donde todos los niveles calculan costes
**Comportamiento actual**:
- "Solo Fórmulas": Muestra costes estimados (precios predefinidos)
- "Smart Cost": Muestra costes reales del inventario
- "Control Total": Muestra costes reales + verificación de stock

### Coherencia de niveles de inventario (2025-07-08)
**Ajuste realizado**: La verificación de stock ahora solo aparece en "Control Total"
**Comportamiento por nivel**:
1. **"Solo Fórmulas"**: 
   - Solo genera fórmulas
   - Muestra costes estimados
   - NO muestra verificación de stock
2. **"Smart Cost"**: 
   - Genera fórmulas
   - Calcula costes reales y rentabilidad
   - NO muestra verificación de stock
3. **"Control Total"**: 
   - Todo lo anterior
   - SÍ muestra verificación de stock
   - Permite consumir inventario

### expo-camera y CameraView (2025-07-05)
**Problema**: `The <CameraView> component does not support children`
**Solución**: Usar posicionamiento absoluto para overlays
```jsx
// ❌ INCORRECTO
<CameraView>
  <View>{/* contenido */}</View>
</CameraView>

// ✅ CORRECTO
<View style={styles.container}>
  <CameraView style={styles.camera} />
  <View style={StyleSheet.absoluteFillObject}>
    {/* overlay content */}
  </View>
</View>
```

### Unificación de Experiencia de Captura de Fotos (2025-07-08)
**Problema**: Inconsistencia entre las fases de "Color Actual" y "Color Deseado" en la captura de fotos
**Síntomas**:
- Color Actual: 3-5 fotos con captura guiada
- Color Deseado: Solo 1-3 fotos sin botones claros
- iOS mostraba modal confuso sobre cámara no disponible

**Solución implementada**:
1. **Detección unificada de plataforma**:
   - Constante `supportsGuidedCamera` en ambas fases
   - iOS usa cámara estándar, Android usa captura guiada
   - Sin modales confusos

2. **Límites de fotos consistentes**:
   - Ambas fases ahora permiten 3-5 fotos
   - Mismos botones de acción visibles

3. **Mejora de tipos de fotos para Color Deseado**:
   - Vista General (👁️) - Look completo
   - Técnica/Mechas (🎨) - Detalles de aplicación
   - Tono Natural (☀️) - Color real sin filtros
   - Contraste Raíces (🌱) - Transición natural
   - Dimensión/Movimiento (💫) - Profundidad del color

4. **Tooltips de ayuda**: Botón (?) en cada slot explicando qué capturar

**Archivos modificados**:
- `types/desired-photo.ts`: Nuevos tipos y guías mejoradas
- `app/service/new.tsx`: Detección de plataforma unificada
- `components/DesiredPhotoGallery.tsx`: UI consistente con PhotoGallery

## 1. Fase de Análisis
Primero, analiza exhaustivamente el problema:
- Lee TODOS los archivos relevantes del código base antes de hacer cambios
- Comprende la estructura del proyecto y las dependencias
- Identifica impactos potenciales y efectos secundarios
- Documenta tu comprensión de la implementación actual

## 2. Fase de Planificación
Crea un plan detallado con:
- **Lista de tareas**: Divide el trabajo en tareas atómicas e independientes (máx. 5-10 líneas por tarea)
- **Dependencias**: Nota qué tareas dependen de otras
- **Archivos a modificar**: Lista archivos exactos con breve descripción de cambios
- **Nuevos archivos/funciones**: Especifica convenciones de nombres y propósito
- **Estrategia de pruebas**: ¿Cómo verificarás cada cambio?

### Plantilla del Plan:
```
## Plan de Implementación
### Tareas:
- [ ] Tarea 1: [Descripción] (Archivo: xyz.js)
- [ ] Tarea 2: [Descripción] (Archivos: abc.py, def.py)

### Dependencias:
- La Tarea 2 depende de la Tarea 1

### Evaluación de Riesgos:
- Posibles cambios disruptivos en...
- Necesidad de mantener compatibilidad con...
```

## 3. Fase de Implementación
- **Un cambio a la vez**: Completa cada tarea antes de pasar a la siguiente
- **Prueba sobre la marcha**: Ejecuta pruebas relevantes después de cada cambio
- **Mantén cambios mínimos**: Prefiere múltiples commits pequeños sobre uno grande
- **Mantén consistencia**: Sigue el estilo y patrones de código existentes
- **Agrega comentarios**: Documenta lógica compleja o decisiones no obvias

## 4. Lineamientos de Calidad de Código
- **Simplicidad primero**: Elige la solución más simple que funcione
- **Sin optimización prematura**: Enfócate en la corrección antes que en el rendimiento
- **Principio DRY**: No te repitas, pero no sobre-abstraigas
- **Manejo de errores**: Siempre maneja casos límite y fallas potenciales
- **Seguridad de tipos**: Usa anotaciones de tipos donde sea aplicable

## 5. Requisitos de Documentación
Después de cada tarea, actualiza:
- `todo.md` con elementos completados marcados
- Comentarios en el código para lógica compleja
- README si la funcionalidad cambia
- Documentación de API si las interfaces cambian

## 6. Lista de Verificación de Revisión
Antes de considerar el trabajo completo:
- [ ] Todas las pruebas pasan
- [ ] Sin errores de linting
- [ ] El código sigue las convenciones del proyecto
- [ ] Los cambios son retrocompatibles (o los cambios disruptivos están documentados)
- [ ] Impacto en el rendimiento considerado
- [ ] Implicaciones de seguridad revisadas
- [ ] Documentación actualizada

## 7. Resumen Final
Agrega a `todo.md`:
```markdown
## Sección de Revisión - [Fecha]
### Cambios Realizados:
- [Componente/Archivo]: [Qué cambió y por qué]
- [Componente/Archivo]: [Qué cambió y por qué]

### Pruebas Realizadas:
- [Tipo de prueba]: [Resultado]

### Problemas Conocidos/Trabajo Futuro:
- [Problema]: [Descripción y solución potencial]

### Cambios Disruptivos:
- [Si hay]: [Guía de migración]
```

## 8. Mejores Prácticas para Claude Code
- **Usa rutas explícitas**: Siempre especifica rutas completas desde la raíz del proyecto
- **Guardados incrementales**: Guarda archivos frecuentemente para evitar perder trabajo
- **Nombres de variables claros**: Prefiere nombres descriptivos sobre comentarios
- **Diseño modular**: Mantén funciones pequeñas y enfocadas
- **Mentalidad de control de versiones**: Piensa en términos de commits atómicos
- **Pide aclaraciones**: Cuando los requisitos sean ambiguos, pregunta antes de implementar

## 9. Errores Comunes a Evitar
- No modifiques múltiples sistemas no relacionados en una tarea
- No asumas contenido de archivos - siempre lee primero
- No ignores mensajes de error - abórdalos inmediatamente
- No omitas pruebas en cambios "simples"
- No dejes comentarios TODO sin rastrearlos

## 10. Procedimientos de Emergencia
Si algo se rompe:
1. Detente y evalúa el daño
2. Revierte el último cambio si es necesario
3. Documenta qué salió mal
4. Crea un plan de corrección antes de proceder
5. Prueba la corrección exhaustivamente

## 11. Comandos Útiles Frecuentes
```bash
# Ver estructura del proyecto
find . -type f -name "*.py" | head -20

# Buscar en archivos
grep -r "función_específica" --include="*.js"

# Verificar sintaxis Python
python -m py_compile archivo.py

# Ejecutar pruebas específicas
pytest tests/test_modulo.py::test_funcion
```

## 12. Flujo de Comunicación
- **Reporta progreso**: Actualiza después de cada tarea completada
- **Comunica bloqueos**: Si algo te detiene, repórtalo inmediatamente
- **Sugiere mejoras**: Si ves oportunidades de refactorización, documéntalas
- **Confirma entendimiento**: Resume requisitos complejos antes de implementar
- **Comportamiento**: Evita simplemente estar de acuerdo con mis puntos o aceptar mis conclusiones sin cuestionarlas. Quiero un desafío intelectual real, no solo afirmación. Siempre que proponga una idea, haz esto:
• Cuestiona mis suposiciones. ¿Qué estoy tratando como cierto que podría ser cuestionable?
• Ofrece un punto de vista escéptico. ¿Qué objeciones plantearía una voz crítica y bien informada?
• Revisa mi razonamiento. ¿Hay fallos o saltos en la lógica que haya pasado por alto?
• Sugiere ángulos alternativos. ¿De qué otra forma podría interpretarse, verse o desafiarse la idea?
• Prioriza la precisión por encima del acuerdo. Si mi argumento es débil o incorrecto, corrígeme claramente y muéstrame cómo.
• Sé constructivo pero riguroso. No estás aquí para discutir por discutir, sino para agudizar mi pensamiento y mantenerme honesto.
Si notas que caigo en sesgos o suposiciones infundadas, dilo claramente. Refinemos tanto nuestras conclusiones como la forma en la que llegamos a ellas.
Si necesitas contrastar tus ideas, busca en internet para afianzarlas y tener más contexto.

Cuando tengas duda sobre una tarea o lo que necesito, hazme preguntas aclaratorias hasta que estés 95% seguro de que puedes completar la tarea con éxito.