# Salon Copilot: Asistente de Coloración Capilar con IA

**Versión:** 1.3.0 | **Estado:** Estable y Funcional ✅

## 📱 Descripción

Salon Copilot es una aplicación móvil profesional diseñada para coloristas y estilistas que revoluciona el proceso de coloración capilar mediante inteligencia artificial y gestión inteligente de inventario.

## ✨ Características Principales

### 🎨 Asistente de Coloración con IA
- **Diagnóstico Inteligente**: Análisis automático del cabello mediante fotos
- **Fórmulas Ultra-Inteligentes**: Generación de fórmulas personalizadas basadas en IA
- **Análisis de Viabilidad**: Evaluación de riesgos antes de aplicar color
- **Historial de Cliente**: Seguimiento completo de la evolución capilar
- **Mantener Color Actual**: Replica fórmulas de otras marcas cuando el cliente está feliz con su color
- **Corrección de Color**: Detección y pasos automáticos para neutralización y pre-pigmentación
- **Instrucciones Paso a Paso Premium**: Flujo visual de 10 pantallas interactivas para guiar todo el proceso de coloración

### 📦 Sistema de Inventario y Costos
- **Control Multinivel**: 
  - Solo Fórmulas: Sin gestión de inventario
  - Smart Cost: Cálculo de costos reales
  - Control Total: Gestión completa con consumo automático
- **Validación de Stock**: Verificación en tiempo real antes de cada servicio
- **Cálculo de Márgenes**: Análisis de rentabilidad instantáneo
- **Consumo Automático**: Actualización de inventario al completar servicios

### 📸 Captura y Análisis de Imágenes
- **Cámara Guiada**: Asistente para capturar fotos desde ángulos óptimos
- **Privacidad Garantizada**: Difuminado facial automático
- **Análisis por Zonas**: Raíces, medios y puntas analizados independientemente

### ⚙️ Configuración Simplificada
- **Configuración de IA**: Solo 2 controles esenciales (Nivel de Análisis y Modo Privacidad)
- **Notificaciones Inteligentes**: Recordatorios de servicios y alertas de inventario
- **Gestión de Datos**: Exportación y control total sobre tu información

## ⚠️ Limitaciones Conocidas

### Cámara en iOS para Color Deseado
- **Limitación**: En iOS, la fase de "Color Deseado" usa la cámara nativa del sistema en lugar de la cámara guiada.
- **Razón**: Bug conocido en expo-camera cuando se usa dentro de Modal en iOS.
- **Impacto**: Sin guías visuales en iOS para esta fase específica.
- **Solución futura**: Se está investigando una solución para mantener la experiencia completa.
- Ver detalles completos en [`docs/CAMERA_CRASH_INVESTIGATION.md`](docs/CAMERA_CRASH_INVESTIGATION.md)

## 🚀 Instalación

### Requisitos Previos
- Node.js 18+ 
- npm o yarn
- Expo CLI (`npm install -g expo-cli`)
- Expo Go app en tu dispositivo móvil

### Pasos de Instalación

1. Clonar el repositorio:
```bash
git clone https://github.com/OscarCortijo/rork-salonier-copilot--asistente-de-coloraci-n-capilar-con-ia.git
cd rork-salonier-copilot--asistente-de-coloraci-n-capilar-con-ia
```

2. Instalar dependencias:
```bash
npm install
```

3. Iniciar la aplicación:
```bash
npx expo start --tunnel
```

4. Escanear el código QR con Expo Go

## 📂 Estructura del Proyecto

```
├── app/                    # Pantallas principales (Expo Router)
│   ├── (tabs)/            # Navegación principal
│   │   ├── index.tsx      # Dashboard
│   │   ├── clients.tsx    # Gestión de clientes
│   │   └── inventory.tsx  # Gestión de inventario
│   ├── service/           # Flujo de servicio
│   │   └── new.tsx        # Nuevo servicio completo
│   └── client/            # Detalles de cliente
├── components/            # Componentes reutilizables
│   ├── inventory/         # Componentes de inventario
│   └── ...               # Otros componentes
├── stores/               # Estado global (Zustand)
│   ├── inventory-store.ts # Gestión de inventario
│   └── salon-config-store.ts # Configuración del salón
├── services/             # Lógica de negocio
│   └── inventoryConsumptionService.ts
└── types/                # Definiciones TypeScript
```

## 🔧 Configuración

### Niveles de Control de Inventario

En la pantalla de configuración, puedes elegir entre:

1. **Solo Fórmulas**: Ideal para freelancers que no necesitan control de inventario
2. **Smart Cost**: Calcula costos reales basados en tu inventario
3. **Control Total**: Gestión completa con consumo automático y alertas de stock

### Configuración de Precios

- Define márgenes de ganancia (0-500%)
- Configura políticas de redondeo
- Establece precio mínimo por servicio
- Gestiona impuestos

## 📱 Uso de la Aplicación

### Nuevo Servicio

1. **Seleccionar Cliente**: Elige o crea un nuevo cliente
2. **Diagnóstico Capilar**: 
   - Captura fotos con guía o usa análisis manual
   - Análisis automático con IA en un solo clic
   - Diagnóstico detallado por zonas (raíces, medios, puntas)
   - Detección de matices no deseados
3. **Resultado Deseado**:
   - Captura referencias con cámara guiada
   - Define técnica y objetivos
   - Análisis de viabilidad automático
4. **Formulación**:
   - Genera fórmula con IA ultra-inteligente
   - **NUEVO**: Conversión automática entre marcas
   - Corrección de color incluida cuando es necesaria
   - Verifica stock disponible en tiempo real
   - Revisa costos y márgenes con precisión
5. **Resultado Final**:
   - Documenta resultado
   - Activa consumo de inventario

### Gestión de Inventario

- **Agregar Productos**: Define productos con precios y stock
- **Movimientos**: Registra compras y ajustes
- **Alertas**: Recibe notificaciones de stock bajo
- **Reportes**: Analiza consumo y rentabilidad

## 🛠️ Tecnologías Utilizadas

- **React Native** con **Expo**
- **TypeScript** para type safety
- **Expo Router** para navegación
- **Zustand** para estado global
- **AsyncStorage** para persistencia
- **Lucide Icons** para iconografía

## 🤝 Contribución

Este proyecto está en desarrollo activo. Para contribuir:

1. Fork el repositorio
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

## 📄 Licencia

Proyecto privado - Todos los derechos reservados

## 👥 Equipo

Desarrollado por el equipo de Rork con la asistencia de Claude AI

## 🆕 Últimas Actualizaciones (2025-07-02)

### ✨ Nuevas Características:
- **Conversión Automática entre Marcas**: Traduce fórmulas de cualquier marca a la que uses
- **Corrección de Color Inteligente**: Detecta y genera pasos de neutralización automáticamente
- **Diagnóstico por Zonas Mejorado**: Análisis profesional con niveles decimales (1.0-10.0)
- **UI Optimizada**: Mejoras en modales, áreas táctiles y navegación

## 🌍 Configuración Regional (2025-07-04)

### Países Soportados
- **Europa**: España 🇪🇸, Francia 🇫🇷, Alemania 🇩🇪, Italia 🇮🇹, Reino Unido 🇬🇧, Portugal 🇵🇹
- **América del Norte**: Estados Unidos 🇺🇸, Canadá 🇨🇦, México 🇲🇽
- **América del Sur**: Brasil 🇧🇷, Argentina 🇦🇷, Chile 🇨🇱, Colombia 🇨🇴

### Características Regionales
- **Sistema de Medición Automático**: Métrico (ml, g) o Imperial (fl oz, oz)
- **Multi-Moneda**: 26 monedas disponibles con conversión automática
- **Formatos Locales**: Fecha, hora, separadores decimales según país
- **Terminología Específica**: Oxidante/Developer, Tinte/Color según región
- **Regulaciones Locales**: Límites de volúmenes de oxidante, pruebas de alergia

### Generación de Fórmulas Regional
- Fórmulas adaptadas al sistema de medición del país
- Instrucciones en el idioma local (ES, EN, PT, FR)
- Conversión automática de unidades
- Cumplimiento de regulaciones por país

### 🐛 Correcciones:
- Navegación post-firma mejorada sin bloqueos
- Validaciones robustas para prevenir errores
- Parseo de fórmulas más flexible
- Mejor manejo de estados asíncronos
- Corrección del error "chemicalDamage" en generación de fórmulas
- Modal de sistema de medidas corregido con contenido visible
- Selector de países con búsqueda integrada

---

Para más información o soporte, contacta al equipo de desarrollo.