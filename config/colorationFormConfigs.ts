/**
 * Configuraciones de formularios para el proceso de coloración
 * Definiciones específicas para cada tipo de consulta
 */

import {
  SmartFormConfig,
  ColorConsultationConfig,
  FieldConfiguration,
  DisclosureLevel,
  DISCLOSURE_LEVELS
} from '@/types/progressive-disclosure';

// Configuración para análisis de color actual
export const CURRENT_COLOR_CONFIG: ColorConsultationConfig = {
  id: 'current_color_analysis',
  type: 'current-color',
  title: 'Análisis de Color Actual',
  description: 'Analiza el estado actual del cabello para crear la base perfecta',
  requiresPhoto: true,
  viabilityCheck: false,
  aiAnalysisRequired: true,
  estimatedTotalTime: 180, // 3 minutos
  levels: [
    {
      ...DISCLOSURE_LEVELS.QUICK_START,
      requiredFields: ['photo_capture', 'hair_goal'],
      optionalFields: []
    },
    {
      ...DISCLOSURE_LEVELS.SMART_CONSULTATION,
      requiredFields: ['overall_level', 'overall_tone', 'hair_state'],
      optionalFields: ['gray_percentage', 'previous_chemical']
    },
    {
      ...DISCLOSURE_LEVELS.EXPERT_MODE,
      requiredFields: [],
      optionalFields: ['zone_analysis', 'porosity_test', 'elasticity_test']
    }
  ],
  fields: [
    // Nivel 1: Quick Start
    {
      id: 'photo_capture',
      type: 'visual-select',
      label: 'Foto del Cabello',
      description: 'Toma una foto clara del cabello en luz natural',
      required: true,
      level: 1,
      aiSuggestion: false,
      contextualHelp: 'La foto debe mostrar claramente el color y estado del cabello. Evita flash y luz artificial.'
    },
    {
      id: 'hair_goal',
      type: 'visual-select',
      label: '¿Qué quieres lograr hoy?',
      description: 'Selecciona el objetivo principal del servicio',
      required: true,
      level: 1,
      aiSuggestion: false,
      validationRules: [
        {
          type: 'options',
          value: [
            'Mantener color actual',
            'Cambio sutil (1-2 niveles)',
            'Transformación dramática',
            'Corrección de color',
            'Cobertura de canas',
            'Refrescar color'
          ],
          message: 'Selecciona un objetivo'
        }
      ]
    },

    // Nivel 2: Smart Consultation
    {
      id: 'overall_level',
      type: 'slider',
      label: 'Nivel de Profundidad General',
      description: 'Nivel promedio del cabello (1=negro, 10=rubio muy claro)',
      required: true,
      level: 2,
      aiSuggestion: true,
      validationRules: [
        { type: 'min', value: 1, message: 'El nivel mínimo es 1' },
        { type: 'max', value: 10, message: 'El nivel máximo es 10' }
      ],
      contextualHelp: 'La IA analiza la foto para sugerir el nivel. Puedes ajustarlo si no coincide con tu percepción.'
    },
    {
      id: 'overall_tone',
      type: 'select',
      label: 'Tono Base Predominante',
      description: 'Tono principal que observas en el cabello',
      required: true,
      level: 2,
      aiSuggestion: true,
      validationRules: [
        {
          type: 'options',
          value: [
            'Natural',
            'Dorado',
            'Cobrizo',
            'Rojizo',
            'Ceniza',
            'Beige',
            'Platino',
            'Violeta'
          ],
          message: 'Selecciona un tono'
        }
      ]
    },
    {
      id: 'hair_state',
      type: 'select',
      label: 'Estado del Cabello',
      description: 'Condición actual del cabello',
      required: true,
      level: 2,
      aiSuggestion: true,
      validationRules: [
        {
          type: 'options',
          value: [
            'Natural (sin procesar)',
            'Teñido anteriormente',
            'Decolorado',
            'Con mechas/reflejos',
            'Permanente/alisado',
            'Dañado/poroso'
          ],
          message: 'Selecciona el estado'
        }
      ]
    },
    {
      id: 'gray_percentage',
      type: 'slider',
      label: 'Porcentaje de Canas',
      description: 'Estimación del porcentaje de canas (solo en raíces)',
      required: false,
      level: 2,
      aiSuggestion: true,
      showCondition: (data) => data.hair_goal === 'Cobertura de canas' || data.overall_level >= 6,
      validationRules: [
        { type: 'min', value: 0, message: 'Mínimo 0%' },
        { type: 'max', value: 100, message: 'Máximo 100%' }
      ]
    },
    {
      id: 'previous_chemical',
      type: 'text',
      label: 'Proceso Químico Previo',
      description: 'Describe el último tratamiento químico realizado',
      required: false,
      level: 2,
      aiSuggestion: false,
      showCondition: (data) => data.hair_state !== 'Natural (sin procesar)',
      placeholder: 'Ej: Decoloración hace 2 meses, tinte permanente...'
    },

    // Nivel 3: Expert Mode
    {
      id: 'zone_analysis',
      type: 'hair-map',
      label: 'Análisis por Zonas',
      description: 'Análisis detallado de raíces, medios y puntas',
      required: false,
      level: 3,
      aiSuggestion: true,
      contextualHelp: 'Toca cada zona del cabello para analizar diferencias de color y estado.'
    },
    {
      id: 'porosity_test',
      type: 'select',
      label: 'Test de Porosidad',
      description: 'Resultado del test de porosidad del cabello',
      required: false,
      level: 3,
      aiSuggestion: false,
      validationRules: [
        {
          type: 'options',
          value: [
            'Baja porosidad',
            'Porosidad normal',
            'Alta porosidad',
            'No realizado'
          ],
          message: 'Selecciona el resultado'
        }
      ],
      contextualHelp: 'Test del vaso de agua: cabello flota (baja), se hunde lentamente (normal), se hunde rápido (alta).'
    },
    {
      id: 'elasticity_test',
      type: 'select',
      label: 'Test de Elasticidad',
      description: 'Resultado del test de elasticidad del cabello',
      required: false,
      level: 3,
      aiSuggestion: false,
      validationRules: [
        {
          type: 'options',
          value: [
            'Buena elasticidad',
            'Elasticidad limitada',
            'Sin elasticidad',
            'No realizado'
          ],
          message: 'Selecciona el resultado'
        }
      ],
      contextualHelp: 'Estira un cabello húmedo: vuelve a su forma (buena), se estira poco (limitada), se rompe (sin elasticidad).'
    }
  ]
};

// Configuración para análisis de color deseado
export const DESIRED_COLOR_CONFIG: ColorConsultationConfig = {
  id: 'desired_color_analysis',
  type: 'desired-color',
  title: 'Color Deseado',
  description: 'Define el color objetivo y valida su viabilidad',
  requiresPhoto: false,
  viabilityCheck: true,
  aiAnalysisRequired: false,
  estimatedTotalTime: 240, // 4 minutos
  levels: [
    {
      ...DISCLOSURE_LEVELS.QUICK_START,
      requiredFields: ['desired_inspiration', 'target_level'],
      optionalFields: ['reference_photo']
    },
    {
      ...DISCLOSURE_LEVELS.SMART_CONSULTATION,
      requiredFields: ['target_tone', 'technique_preference'],
      optionalFields: ['maintenance_level', 'budget_range']
    },
    {
      ...DISCLOSURE_LEVELS.EXPERT_MODE,
      requiredFields: [],
      optionalFields: ['zone_customization', 'advanced_technique', 'timeline_preference']
    }
  ],
  fields: [
    // Nivel 1: Quick Start
    {
      id: 'desired_inspiration',
      type: 'visual-select',
      label: 'Inspiración de Color',
      description: 'Selecciona el estilo de color que te gusta',
      required: true,
      level: 1,
      aiSuggestion: false,
      validationRules: [
        {
          type: 'options',
          value: [
            'Rubio natural',
            'Rubio platino',
            'Castaño chocolate',
            'Pelirrojo vibrante',
            'Moreno intenso',
            'Colores fantasía',
            'Mechas/balayage',
            'Ombré/degradado'
          ],
          message: 'Selecciona una inspiración'
        }
      ]
    },
    {
      id: 'target_level',
      type: 'color-picker',
      label: 'Nivel Objetivo',
      description: 'Nivel de claridad deseado (1=negro, 10=rubio muy claro)',
      required: true,
      level: 1,
      aiSuggestion: true,
      validationRules: [
        { type: 'min', value: 1, message: 'El nivel mínimo es 1' },
        { type: 'max', value: 10, message: 'El nivel máximo es 10' }
      ]
    },
    {
      id: 'reference_photo',
      type: 'visual-select',
      label: 'Foto de Referencia',
      description: 'Sube una foto del color que quieres lograr (opcional)',
      required: false,
      level: 1,
      aiSuggestion: false,
      contextualHelp: 'Una foto de referencia ayuda a entender mejor tus expectativas y calcular la viabilidad.'
    },

    // Nivel 2: Smart Consultation
    {
      id: 'target_tone',
      type: 'color-picker',
      label: 'Tono Deseado',
      description: 'Matiz específico que quieres lograr',
      required: true,
      level: 2,
      aiSuggestion: true,
      validationRules: [
        {
          type: 'options',
          value: [
            'Natural',
            'Dorado',
            'Cobrizo',
            'Rojizo',
            'Ceniza',
            'Beige',
            'Platino',
            'Violeta',
            'Rosa',
            'Azul',
            'Verde',
            'Personalizado'
          ],
          message: 'Selecciona un tono'
        }
      ]
    },
    {
      id: 'technique_preference',
      type: 'visual-select',
      label: 'Técnica Preferida',
      description: 'Método de aplicación que prefieres',
      required: true,
      level: 2,
      aiSuggestion: true,
      validationRules: [
        {
          type: 'options',
          value: [
            'Color uniforme',
            'Mechas tradicionales',
            'Balayage',
            'Ombré',
            'Babylights',
            'Chunky highlights',
            'Color melting',
            'Técnica mixta'
          ],
          message: 'Selecciona una técnica'
        }
      ]
    },
    {
      id: 'maintenance_level',
      type: 'select',
      label: 'Nivel de Mantenimiento',
      description: 'Frecuencia de retoque que prefieres',
      required: false,
      level: 2,
      aiSuggestion: false,
      validationRules: [
        {
          type: 'options',
          value: [
            'Bajo (cada 3-4 meses)',
            'Medio (cada 2-3 meses)',
            'Alto (cada 4-6 semanas)',
            'Muy alto (cada 2-4 semanas)'
          ],
          message: 'Selecciona el nivel de mantenimiento'
        }
      ]
    },
    {
      id: 'budget_range',
      type: 'select',
      label: 'Rango de Presupuesto',
      description: 'Presupuesto aproximado para el servicio',
      required: false,
      level: 2,
      aiSuggestion: false,
      validationRules: [
        {
          type: 'options',
          value: [
            'Económico (€50-100)',
            'Medio (€100-200)',
            'Premium (€200-350)',
            'Lujo (€350+)',
            'Sin límite'
          ],
          message: 'Selecciona el rango de presupuesto'
        }
      ]
    },

    // Nivel 3: Expert Mode
    {
      id: 'zone_customization',
      type: 'hair-map',
      label: 'Personalización por Zonas',
      description: 'Diferentes colores/técnicas para cada zona',
      required: false,
      level: 3,
      aiSuggestion: false,
      contextualHelp: 'Permite aplicar diferentes colores o intensidades en raíces, medios y puntas.'
    },
    {
      id: 'advanced_technique',
      type: 'text',
      label: 'Técnica Avanzada',
      description: 'Especificaciones técnicas adicionales',
      required: false,
      level: 3,
      aiSuggestion: false,
      placeholder: 'Ej: Degradado específico, patrones de mechas, efectos especiales...'
    },
    {
      id: 'timeline_preference',
      type: 'select',
      label: 'Preferencia de Tiempo',
      description: 'Cómo prefieres distribuir el proceso',
      required: false,
      level: 3,
      aiSuggestion: false,
      validationRules: [
        {
          type: 'options',
          value: [
            'Una sola sesión',
            'Dos sesiones (más seguro)',
            'Proceso gradual (3+ sesiones)',
            'Sin preferencia'
          ],
          message: 'Selecciona la preferencia de tiempo'
        }
      ]
    }
  ]
};

// Configuración para consulta completa
export const FULL_CONSULTATION_CONFIG: ColorConsultationConfig = {
  id: 'full_consultation',
  type: 'full-consultation',
  title: 'Consulta Completa de Coloración',
  description: 'Análisis integral desde el color actual hasta el resultado final',
  requiresPhoto: true,
  viabilityCheck: true,
  aiAnalysisRequired: true,
  estimatedTotalTime: 420, // 7 minutos
  levels: [
    {
      ...DISCLOSURE_LEVELS.QUICK_START,
      requiredFields: ['photos_current', 'goal_definition', 'inspiration'],
      optionalFields: []
    },
    {
      ...DISCLOSURE_LEVELS.SMART_CONSULTATION,
      requiredFields: ['current_analysis', 'desired_specification', 'viability_check'],
      optionalFields: ['lifestyle_factors', 'preferences']
    },
    {
      ...DISCLOSURE_LEVELS.EXPERT_MODE,
      requiredFields: [],
      optionalFields: ['detailed_analysis', 'custom_formulation', 'advanced_options']
    }
  ],
  fields: [
    // Combinar campos de ambas configuraciones anteriores
    // con lógica de dependencias y flujo optimizado
  ]
};

// Función para obtener configuración por tipo
export function getFormConfig(type: 'current-color' | 'desired-color' | 'full-consultation'): ColorConsultationConfig {
  switch (type) {
    case 'current-color':
      return CURRENT_COLOR_CONFIG;
    case 'desired-color':
      return DESIRED_COLOR_CONFIG;
    case 'full-consultation':
      return FULL_CONSULTATION_CONFIG;
    default:
      return CURRENT_COLOR_CONFIG;
  }
}

// Función para personalizar configuración según contexto
export function customizeFormConfig(
  baseConfig: ColorConsultationConfig,
  context: {
    professionalLevel?: 'beginner' | 'intermediate' | 'expert';
    timeAvailable?: number;
    clientType?: 'new' | 'returning';
    serviceType?: 'consultation' | 'full-service';
  }
): ColorConsultationConfig {
  const customConfig = { ...baseConfig };

  // Ajustar según nivel profesional
  if (context.professionalLevel === 'expert') {
    // Permitir comenzar en nivel 2 para expertos
    customConfig.levels[0].priority = 2;
  } else if (context.professionalLevel === 'beginner') {
    // Hacer más campos requeridos para principiantes
    customConfig.fields.forEach(field => {
      if (field.level <= 2 && field.contextualHelp) {
        field.required = true;
      }
    });
  }

  // Ajustar según tiempo disponible
  if (context.timeAvailable && context.timeAvailable < 300) {
    // Menos de 5 minutos: solo nivel 1 y 2
    customConfig.fields = customConfig.fields.filter(field => field.level <= 2);
    customConfig.levels = customConfig.levels.filter(level => level.priority <= 2);
  }

  // Ajustar según tipo de cliente
  if (context.clientType === 'returning') {
    // Pre-llenar algunos campos basándose en historial
    customConfig.fields.forEach(field => {
      if (field.id.includes('previous') || field.id.includes('history')) {
        field.aiSuggestion = true;
      }
    });
  }

  return customConfig;
}
