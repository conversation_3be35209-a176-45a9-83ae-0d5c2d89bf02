# Contexto para la Próxima Sesión - Salonier Copilot

## Estado Actual del Proyecto (2025-07-05)

### ✅ Funcionalidades Completadas

1. **Sistema de Diseño Premium**
   - Paleta de colores dorados/cálidos implementada
   - Componentes base creados (BaseCard, BaseButton, etc.)
   - Pantallas principales actualizadas con el nuevo diseño

2. **Flujo de Servicio Completo**
   - Diagnóstico capilar con IA
   - Captura de resultado deseado
   - Formulación inteligente
   - Instrucciones paso a paso
   - Registro de resultado final

3. **Sistema de Cámara Guiada**
   - ✅ Funciona perfectamente en fase de Diagnóstico (iOS y Android)
   - ⚠️ En Resultado Deseado: Android funciona, iOS usa fallback a cámara estándar

4. **Características Avanzadas**
   - Parser inteligente de fórmulas
   - Conversión entre marcas de tintes
   - Control de inventario con consumo automático
   - Historial de clientes con recomendaciones

### ⚠️ Problema Conocido: Cámara en iOS

**Descripción**: La cámara guiada crashea en iOS cuando se usa en la fase de "Resultado Deseado" debido a un bug de expo-camera con Modals en contextos complejos.

**Solución Actual**: Fallback automático a ImagePicker en iOS con mensaje explicativo al usuario.

**Archivos clave**:
- `docs/CAMERA_CRASH_INVESTIGATION.md` - Historial completo de intentos
- `app/service/new.tsx` - Líneas 1750-1850 contienen el fallback

### 📁 Estructura del Proyecto

```
/app
  /(tabs)        - Pantallas principales (actualizadas con nuevo diseño)
  /service       - Flujo de servicio (new.tsx es el archivo principal)
  /client        - Gestión de clientes
  /inventory     - Control de inventario

/components
  /base          - Componentes del sistema de diseño
  /formulation   - Componentes de formulación
  GuidedCamera.tsx - Cámara con guías visuales

/stores          - Estados globales (Zustand)
/services        - Lógica de negocio
/types           - Definiciones TypeScript
/constants       - Colores y tema
```

### 🔧 Configuración Importante

1. **Modo de desarrollo**: La app está en modo desarrollo con datos mock
2. **Control de inventario**: Configurable en ajustes (solo fórmulas o control total)
3. **Análisis IA**: Usa un servicio mock que simula respuestas reales

### 📝 Para Continuar el Desarrollo

1. **Bug de Cámara iOS**: 
   - Monitorear actualizaciones de expo-camera
   - El fallback actual es funcional pero no ideal
   - Considerar alternativas como react-native-vision-camera

2. **Diseño Pendiente**:
   - Actualizar pantallas secundarias con el nuevo sistema de diseño
   - Archivos: `inventory/[id].tsx`, `inventory/edit/[id].tsx`, `service/*`

3. **Mejoras Sugeridas**:
   - Sistema de notificaciones para temporizadores
   - Exportación de fórmulas a PDF
   - Modo offline con sincronización

### 🚀 Comandos Útiles

```bash
# Desarrollo
npm start

# Limpiar caché
npx expo start -c

# Build para iOS
eas build --platform ios

# Build para Android
eas build --platform android
```

### 💡 Notas Importantes

1. El proyecto usa Expo SDK 51 con expo-router para navegación
2. La autenticación está mockeada - implementar backend real antes de producción
3. Los permisos de cámara se manejan automáticamente
4. El diseño es responsive pero optimizado para móviles

### 🐛 Si Encuentras Problemas

1. **Cámara no funciona**: Verificar permisos en configuración del dispositivo
2. **Errores de navegación**: Limpiar caché con `npx expo start -c`
3. **Problemas de rendimiento**: La app tiene muchos logs de debug - desactivar en producción

---

Este documento proporciona el contexto necesario para continuar el desarrollo sin perder el progreso actual.